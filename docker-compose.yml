version: "3.8"

services:
  # Reverse Proxy
  traefik:
    image: traefik:v2.10
    container_name: zorgportaal-traefik
    command:
      - "--api.insecure=true"
      - "--providers.docker=true"
      - "--providers.docker.exposedbydefault=false"
      - "--entrypoints.web.address=:80"
      - "--entrypoints.websecure.address=:443"
    ports:
      - "80:80"
      - "443:443"
      - "8080:8080" # Traefik dashboard
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
    networks:
      - zorgportaal-network

  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: zorgportaal-postgres
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-zorgportaal}
      POSTGRES_USER: ${POSTGRES_USER:-zorgportaal}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-secure_password}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    networks:
      - zorgportaal-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-zorgportaal}"]
      interval: 30s
      timeout: 10s
      retries: 3

  # FastAPI Backend
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: zorgportaal-backend
    environment:
      - DATABASE_URL=postgresql://${POSTGRES_USER:-zorgportaal}:${POSTGRES_PASSWORD:-secure_password}@postgres:5432/${POSTGRES_DB:-zorgportaal}
      - JWT_SECRET_KEY=${JWT_SECRET_KEY:-your-secret-key-change-in-production}
      - WHISPER_SERVICE_URL=https://api.runpod.ai/v2/2n7nj4xtwcgsl9
      - RUNPOD_API_KEY="rpa_8OE1WN9C8SFIUSDKPK4TU56NDDULZT6IU8LETSTV11nz9o"
      - LLM_API_KEY=${LLM_API_KEY:-}
      - CLIENT_ID=${CLIENT_ID:-default}
    volumes:
      - ./backend:/app
      - audio_uploads:/app/uploads
    ports:
      - "8000:8000"
    depends_on:
      postgres:
        condition: service_healthy
      # whisper:
      #   condition: service_started
    networks:
      - zorgportaal-network
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.backend.rule=Host(`${DOMAIN:-localhost}`) && PathPrefix(`/api`)"
      - "traefik.http.services.backend.loadbalancer.server.port=8000"
      - "traefik.http.middlewares.backend-stripprefix.stripprefix.prefixes=/api"
      - "traefik.http.routers.backend.middlewares=backend-stripprefix"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Whisper Transcription Service
  # whisper:
  #   build:
  #     context: ./whisper-service
  #     dockerfile: Dockerfile
  #   container_name: zorgportaal-whisper
  #   environment:
  #     - MODEL_SIZE=${WHISPER_MODEL_SIZE:-base}
  #     - DEVICE=${WHISPER_DEVICE:-cpu}
  #   volumes:
  #     - whisper_models:/app/models
  #     - audio_uploads:/app/uploads:ro
  #   ports:
  #     - "8001:8001"
  #   networks:
  #     - zorgportaal-network
  #   healthcheck:
  #     test: ["CMD", "curl", "-f", "http://localhost:8001/health"]
  #     interval: 30s
  #     timeout: 10s
  #     retries: 3

  # React Frontend
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      args:
        - REACT_APP_API_URL=${REACT_APP_API_URL:-http://localhost/api}
        - PUBLIC_URL=${PUBLIC_URL:-}
    container_name: zorgportaal-frontend
    restart: unless-stopped
    networks:
      - zorgportaal-network
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.frontend.rule=Host(`${DOMAIN:-localhost}`) && !PathPrefix(`/api`)"
      - "traefik.http.routers.frontend.priority=1"
      - "traefik.http.services.frontend.loadbalancer.server.port=80"
    depends_on:
      - backend
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis for caching and sessions
  redis:
    image: redis:7-alpine
    container_name: zorgportaal-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - zorgportaal-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  postgres_data:
  redis_data:
  whisper_models:
  audio_uploads:

networks:
  zorgportaal-network:
    driver: bridge
