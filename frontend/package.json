{"name": "zorgportaal-plus-frontend", "version": "2.0.0", "description": "ZorgPortaal Plus - AI-gebaseerd zorgverslagplatform frontend", "private": true, "dependencies": {"@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "@types/node": "^20.8.0", "@types/react": "^18.2.25", "@types/react-dom": "^18.2.10", "axios": "^1.5.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.47.0", "react-query": "^3.39.3", "react-router-dom": "^6.16.0", "react-scripts": "^5.0.1", "typescript": "^4.9.5", "web-vitals": "^3.4.0"}, "devDependencies": {"@tailwindcss/forms": "^0.5.6", "@testing-library/jest-dom": "^6.1.4", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.5.1", "autoprefixer": "^10.4.16", "postcss": "^8.4.31", "tailwindcss": "^3.3.5"}, "scripts": {"start": "SKIP_PREFLIGHT_CHECK=true TSC_COMPILE_ON_ERROR=true react-scripts start", "build": "SKIP_PREFLIGHT_CHECK=true TSC_COMPILE_ON_ERROR=true react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:8000", "overrides": {"ajv": "^6.12.6", "ajv-keywords": "^3.5.2", "ajv-formats": "^1.6.1", "schema-utils": "^3.3.0"}}