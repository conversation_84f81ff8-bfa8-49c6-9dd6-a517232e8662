{"version": 3, "file": "useSuspenseQueries.esm.js", "sources": ["../../src/useSuspenseQueries.ts"], "sourcesContent": ["import { useQueries } from './useQueries'\nimport type {\n  UseQueryOptions,\n  UseSuspenseQueryOptions,\n  UseSuspenseQueryResult,\n} from './types'\nimport type { NetworkMode, QueryFunction } from '@tanstack/query-core'\n\n// Avoid TS depth-limit error in case of large array literal\ntype MAXIMUM_DEPTH = 20\n\ntype GetSuspenseOptions<T> =\n  // Part 1: responsible for applying explicit type parameter to function arguments, if object { queryFnData: TQueryFnData, error: TError, data: TData }\n  T extends {\n    queryFnData: infer TQueryFnData\n    error?: infer TError\n    data: infer TData\n  }\n    ? UseSuspenseQueryOptions<TQueryFnData, TError, TData>\n    : T extends { queryFnData: infer TQueryFnData; error?: infer TError }\n    ? UseSuspenseQueryOptions<TQueryFnData, TError>\n    : T extends { data: infer TData; error?: infer TError }\n    ? UseSuspenseQueryOptions<unknown, TError, TData>\n    : // Part 2: responsible for applying explicit type parameter to function arguments, if tuple [TQueryFnData, TError, TData]\n    T extends [infer TQueryFnData, infer TError, infer TData]\n    ? UseSuspenseQueryOptions<TQueryFnData, TError, TData>\n    : T extends [infer TQueryFnData, infer TError]\n    ? UseSuspenseQueryOptions<TQueryFnData, TError>\n    : T extends [infer TQueryFnData]\n    ? UseSuspenseQueryOptions<TQueryFnData>\n    : // Part 3: responsible for inferring and enforcing type if no explicit parameter was provided\n    T extends {\n        queryFn?: QueryFunction<infer TQueryFnData, infer TQueryKey>\n        select?: (data: any) => infer TData\n      }\n    ? UseSuspenseQueryOptions<TQueryFnData, unknown, TData, TQueryKey>\n    : T extends {\n        queryFn?: QueryFunction<infer TQueryFnData, infer TQueryKey>\n      }\n    ? UseSuspenseQueryOptions<TQueryFnData, unknown, TQueryFnData, TQueryKey>\n    : // Fallback\n      UseSuspenseQueryOptions\n\ntype GetSuspenseResults<T> =\n  // Part 1: responsible for mapping explicit type parameter to function result, if object\n  T extends { queryFnData: any; error?: infer TError; data: infer TData }\n    ? UseSuspenseQueryResult<TData, TError>\n    : T extends { queryFnData: infer TQueryFnData; error?: infer TError }\n    ? UseSuspenseQueryResult<TQueryFnData, TError>\n    : T extends { data: infer TData; error?: infer TError }\n    ? UseSuspenseQueryResult<TData, TError>\n    : // Part 2: responsible for mapping explicit type parameter to function result, if tuple\n    T extends [any, infer TError, infer TData]\n    ? UseSuspenseQueryResult<TData, TError>\n    : T extends [infer TQueryFnData, infer TError]\n    ? UseSuspenseQueryResult<TQueryFnData, TError>\n    : T extends [infer TQueryFnData]\n    ? UseSuspenseQueryResult<TQueryFnData>\n    : // Part 3: responsible for mapping inferred type to results, if no explicit parameter was provided\n    T extends {\n        queryFn?: QueryFunction<infer TQueryFnData, any>\n        select?: (data: any) => infer TData\n      }\n    ? UseSuspenseQueryResult<unknown extends TData ? TQueryFnData : TData>\n    : T extends {\n        queryFn?: QueryFunction<infer TQueryFnData, any>\n      }\n    ? UseSuspenseQueryResult<TQueryFnData>\n    : // Fallback\n      UseSuspenseQueryResult\n\n/**\n * SuspenseQueriesOptions reducer recursively unwraps function arguments to infer/enforce type param\n */\nexport type SuspenseQueriesOptions<\n  T extends Array<any>,\n  TResult extends Array<any> = [],\n  TDepth extends ReadonlyArray<number> = [],\n> = TDepth['length'] extends MAXIMUM_DEPTH\n  ? Array<UseSuspenseQueryOptions>\n  : T extends []\n  ? []\n  : T extends [infer Head]\n  ? [...TResult, GetSuspenseOptions<Head>]\n  : T extends [infer Head, ...infer Tail]\n  ? SuspenseQueriesOptions<\n      [...Tail],\n      [...TResult, GetSuspenseOptions<Head>],\n      [...TDepth, 1]\n    >\n  : Array<unknown> extends T\n  ? T\n  : // If T is *some* array but we couldn't assign unknown[] to it, then it must hold some known/homogenous type!\n  // use this to infer the param types in the case of Array.map() argument\n  T extends Array<\n      UseSuspenseQueryOptions<\n        infer TQueryFnData,\n        infer TError,\n        infer TData,\n        infer TQueryKey\n      >\n    >\n  ? Array<UseSuspenseQueryOptions<TQueryFnData, TError, TData, TQueryKey>>\n  : // Fallback\n    Array<UseSuspenseQueryOptions>\n\n/**\n * SuspenseQueriesResults reducer recursively maps type param to results\n */\nexport type SuspenseQueriesResults<\n  T extends Array<any>,\n  TResult extends Array<any> = [],\n  TDepth extends ReadonlyArray<number> = [],\n> = TDepth['length'] extends MAXIMUM_DEPTH\n  ? Array<UseSuspenseQueryResult>\n  : T extends []\n  ? []\n  : T extends [infer Head]\n  ? [...TResult, GetSuspenseResults<Head>]\n  : T extends [infer Head, ...infer Tail]\n  ? SuspenseQueriesResults<\n      [...Tail],\n      [...TResult, GetSuspenseResults<Head>],\n      [...TDepth, 1]\n    >\n  : T extends Array<\n      UseSuspenseQueryOptions<\n        infer TQueryFnData,\n        infer TError,\n        infer TData,\n        any\n      >\n    >\n  ? // Dynamic-size (homogenous) UseQueryOptions array: map directly to array of results\n    Array<\n      UseSuspenseQueryResult<\n        unknown extends TData ? TQueryFnData : TData,\n        TError\n      >\n    >\n  : // Fallback\n    Array<UseSuspenseQueryResult>\n\nexport function useSuspenseQueries<T extends any[]>({\n  queries,\n  context,\n}: {\n  queries: readonly [...SuspenseQueriesOptions<T>]\n  context?: UseQueryOptions['context']\n}): SuspenseQueriesResults<T> {\n  return useQueries({\n    queries: queries.map((query) => ({\n      ...query,\n      enabled: true,\n      useErrorBoundary: true,\n      suspense: true,\n      placeholderData: undefined,\n      networkMode: 'always' as NetworkMode,\n    })),\n    context,\n  }) as SuspenseQueriesResults<T>\n}\n"], "names": ["useSuspenseQueries", "queries", "context", "useQueries", "map", "query", "enabled", "useErrorBoundary", "suspense", "placeholderData", "undefined", "networkMode"], "mappings": ";;AA+IO,SAASA,kBAAT,CAA6C;EAClDC,OADkD;AAElDC,EAAAA,OAAAA;AAFkD,CAA7C,EAMuB;AAC5B,EAAA,OAAOC,UAAU,CAAC;IAChBF,OAAO,EAAEA,OAAO,CAACG,GAAR,CAAaC,KAAD,KAAY,EAC/B,GAAGA,KAD4B;AAE/BC,MAAAA,OAAO,EAAE,IAFsB;AAG/BC,MAAAA,gBAAgB,EAAE,IAHa;AAI/BC,MAAAA,QAAQ,EAAE,IAJqB;AAK/BC,MAAAA,eAAe,EAAEC,SALc;AAM/BC,MAAAA,WAAW,EAAE,QAAA;AANkB,KAAZ,CAAZ,CADO;AAShBT,IAAAA,OAAAA;AATgB,GAAD,CAAjB,CAAA;AAWD;;;;"}