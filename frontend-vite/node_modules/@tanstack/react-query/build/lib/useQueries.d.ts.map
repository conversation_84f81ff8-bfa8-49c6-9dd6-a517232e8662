{"version": 3, "file": "useQueries.d.ts", "sourceRoot": "", "sources": ["../../src/useQueries.ts"], "names": [], "mappings": "AAmBA,OAAO,KAAK,EAAE,SAAS,EAAE,aAAa,EAAE,QAAQ,EAAE,MAAM,sBAAsB,CAAA;AAC9E,OAAO,KAAK,EACV,qBAAqB,EACrB,eAAe,EACf,cAAc,EACf,MAAM,SAAS,CAAA;AAIhB,aAAK,4BAA4B,CAC/B,YAAY,GAAG,OAAO,EACtB,MAAM,GAAG,OAAO,EAChB,KAAK,GAAG,YAAY,EACpB,SAAS,SAAS,QAAQ,GAAG,QAAQ,IACnC,SAAS,CACX,eAAe,CAAC,YAAY,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,CAAC,EACvD,SAAS,CACV,CAAA;AAGD,aAAK,aAAa,GAAG,EAAE,CAAA;AAEvB,aAAK,UAAU,CAAC,CAAC,IAEf,CAAC,SAAS;IACR,WAAW,EAAE,MAAM,YAAY,CAAA;IAC/B,KAAK,CAAC,EAAE,MAAM,MAAM,CAAA;IACpB,IAAI,EAAE,MAAM,KAAK,CAAA;CAClB,GACG,4BAA4B,CAAC,YAAY,EAAE,MAAM,EAAE,KAAK,CAAC,GACzD,CAAC,SAAS;IAAE,WAAW,EAAE,MAAM,YAAY,CAAC;IAAC,KAAK,CAAC,EAAE,MAAM,MAAM,CAAA;CAAE,GACnE,4BAA4B,CAAC,YAAY,EAAE,MAAM,CAAC,GAClD,CAAC,SAAS;IAAE,IAAI,EAAE,MAAM,KAAK,CAAC;IAAC,KAAK,CAAC,EAAE,MAAM,MAAM,CAAA;CAAE,GACrD,4BAA4B,CAAC,OAAO,EAAE,MAAM,EAAE,KAAK,CAAC,GAEtD,CAAC,SAAS,CAAC,MAAM,YAAY,EAAE,MAAM,MAAM,EAAE,MAAM,KAAK,CAAC,GACvD,4BAA4B,CAAC,YAAY,EAAE,MAAM,EAAE,KAAK,CAAC,GACzD,CAAC,SAAS,CAAC,MAAM,YAAY,EAAE,MAAM,MAAM,CAAC,GAC5C,4BAA4B,CAAC,YAAY,EAAE,MAAM,CAAC,GAClD,CAAC,SAAS,CAAC,MAAM,YAAY,CAAC,GAC9B,4BAA4B,CAAC,YAAY,CAAC,GAE5C,CAAC,SAAS;IACN,OAAO,CAAC,EAAE,aAAa,CAAC,MAAM,YAAY,EAAE,MAAM,SAAS,CAAC,CAAA;IAC5D,MAAM,EAAE,CAAC,IAAI,EAAE,GAAG,KAAK,MAAM,KAAK,CAAA;CACnC,GACD,4BAA4B,CAAC,YAAY,EAAE,OAAO,EAAE,KAAK,EAAE,SAAS,CAAC,GACrE,CAAC,SAAS;IAAE,OAAO,CAAC,EAAE,aAAa,CAAC,MAAM,YAAY,EAAE,MAAM,SAAS,CAAC,CAAA;CAAE,GAC1E,4BAA4B,CAC1B,YAAY,EACZ,OAAO,EACP,YAAY,EACZ,SAAS,CACV,GAED,4BAA4B,CAAA;AAGlC,aAAK,gCAAgC,CAAC,CAAC,EAAE,KAAK,EAAE,MAAM,GAAG,OAAO,IAAI,CAAC,SAAS;IAC5E,WAAW,CAAC,EAAE,MAAM,YAAY,CAAA;CACjC,GACG,OAAO,SAAS,YAAY,GAC1B,cAAc,CAAC,KAAK,EAAE,MAAM,CAAC,GAC7B,YAAY,SAAS,KAAK,GAC1B,qBAAqB,CAAC,KAAK,EAAE,MAAM,CAAC,GACpC,YAAY,SAAS,MAAM,MAAM,kBAAkB,GACnD,OAAO,SAAS,kBAAkB,GAChC,cAAc,CAAC,KAAK,EAAE,MAAM,CAAC,GAC7B,kBAAkB,SAAS,KAAK,GAChC,qBAAqB,CAAC,KAAK,EAAE,MAAM,CAAC,GACpC,cAAc,CAAC,KAAK,EAAE,MAAM,CAAC,GAC/B,cAAc,CAAC,KAAK,EAAE,MAAM,CAAC,GAC/B,cAAc,CAAC,KAAK,EAAE,MAAM,CAAC,CAAA;AAEjC,aAAK,iBAAiB,CAAC,CAAC,IAEtB,CAAC,SAAS;IAAE,WAAW,EAAE,GAAG,CAAC;IAAC,KAAK,CAAC,EAAE,MAAM,MAAM,CAAC;IAAC,IAAI,EAAE,MAAM,KAAK,CAAA;CAAE,GACnE,gCAAgC,CAAC,CAAC,EAAE,KAAK,EAAE,MAAM,CAAC,GAClD,CAAC,SAAS;IAAE,WAAW,EAAE,MAAM,YAAY,CAAC;IAAC,KAAK,CAAC,EAAE,MAAM,MAAM,CAAA;CAAE,GACnE,gCAAgC,CAAC,CAAC,EAAE,YAAY,EAAE,MAAM,CAAC,GACzD,CAAC,SAAS;IAAE,IAAI,EAAE,MAAM,KAAK,CAAC;IAAC,KAAK,CAAC,EAAE,MAAM,MAAM,CAAA;CAAE,GACrD,gCAAgC,CAAC,CAAC,EAAE,KAAK,EAAE,MAAM,CAAC,GAEpD,CAAC,SAAS,CAAC,GAAG,EAAE,MAAM,MAAM,EAAE,MAAM,KAAK,CAAC,GACxC,gCAAgC,CAAC,CAAC,EAAE,KAAK,EAAE,MAAM,CAAC,GAClD,CAAC,SAAS,CAAC,MAAM,YAAY,EAAE,MAAM,MAAM,CAAC,GAC5C,gCAAgC,CAAC,CAAC,EAAE,YAAY,EAAE,MAAM,CAAC,GACzD,CAAC,SAAS,CAAC,MAAM,YAAY,CAAC,GAC9B,gCAAgC,CAAC,CAAC,EAAE,YAAY,CAAC,GAEnD,CAAC,SAAS;IACN,OAAO,CAAC,EAAE,aAAa,CAAC,OAAO,EAAE,GAAG,CAAC,CAAA;IACrC,MAAM,EAAE,CAAC,IAAI,EAAE,GAAG,KAAK,MAAM,KAAK,CAAA;CACnC,GACD,gCAAgC,CAAC,CAAC,EAAE,KAAK,CAAC,GAC1C,CAAC,SAAS;IAAE,OAAO,CAAC,EAAE,aAAa,CAAC,MAAM,YAAY,EAAE,GAAG,CAAC,CAAA;CAAE,GAC9D,gCAAgC,CAAC,CAAC,EAAE,YAAY,CAAC,GAEjD,cAAc,CAAA;AAEpB;;GAEG;AACH,oBAAY,cAAc,CACxB,CAAC,SAAS,GAAG,EAAE,EACf,OAAO,SAAS,GAAG,EAAE,GAAG,EAAE,EAC1B,MAAM,SAAS,aAAa,CAAC,MAAM,CAAC,GAAG,EAAE,IACvC,MAAM,CAAC,QAAQ,CAAC,SAAS,aAAa,GACtC,4BAA4B,EAAE,GAC9B,CAAC,SAAS,EAAE,GACZ,EAAE,GACF,CAAC,SAAS,CAAC,MAAM,IAAI,CAAC,GACtB,CAAC,GAAG,OAAO,EAAE,UAAU,CAAC,IAAI,CAAC,CAAC,GAC9B,CAAC,SAAS,CAAC,MAAM,IAAI,EAAE,GAAG,MAAM,IAAI,CAAC,GACrC,cAAc,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC,GAAG,OAAO,EAAE,UAAU,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,CAAC,CAAC,GACzE,OAAO,EAAE,SAAS,CAAC,GACnB,CAAC,GAGH,CAAC,SAAS,4BAA4B,CAClC,MAAM,YAAY,EAClB,MAAM,MAAM,EACZ,MAAM,KAAK,EACX,MAAM,SAAS,CAChB,EAAE,GACH,4BAA4B,CAAC,YAAY,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,CAAC,EAAE,GAEtE,4BAA4B,EAAE,CAAA;AAElC;;GAEG;AACH,oBAAY,cAAc,CACxB,CAAC,SAAS,GAAG,EAAE,EACf,QAAQ,SAAS,GAAG,EAAE,GAAG,EAAE,EAC3B,MAAM,SAAS,aAAa,CAAC,MAAM,CAAC,GAAG,EAAE,IACvC,MAAM,CAAC,QAAQ,CAAC,SAAS,aAAa,GACtC,cAAc,EAAE,GAChB,CAAC,SAAS,EAAE,GACZ,EAAE,GACF,CAAC,SAAS,CAAC,MAAM,IAAI,CAAC,GACtB,CAAC,GAAG,QAAQ,EAAE,iBAAiB,CAAC,IAAI,CAAC,CAAC,GACtC,CAAC,SAAS,CAAC,MAAM,IAAI,EAAE,GAAG,MAAM,IAAI,CAAC,GACrC,cAAc,CACZ;IAAC,GAAG,IAAI;CAAC,EACT;IAAC,GAAG,QAAQ;IAAE,iBAAiB,CAAC,IAAI,CAAC;CAAC,EACtC;IAAC,GAAG,MAAM;IAAE,CAAC;CAAC,CACf,GACD,CAAC,SAAS,4BAA4B,CACpC,MAAM,YAAY,EAClB,MAAM,MAAM,EACZ,MAAM,KAAK,EACX,GAAG,CACJ,EAAE,GAEH,cAAc,CAAC,OAAO,SAAS,KAAK,GAAG,YAAY,GAAG,KAAK,EAAE,MAAM,CAAC,EAAE,GAEtE,cAAc,EAAE,CAAA;AAEpB,wBAAgB,UAAU,CAAC,CAAC,SAAS,GAAG,EAAE,EAAE,EAC1C,OAAO,EACP,OAAO,GACR,EAAE;IACD,OAAO,EAAE,SAAS,CAAC,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC,CAAA;IACxC,OAAO,CAAC,EAAE,eAAe,CAAC,SAAS,CAAC,CAAA;CACrC,GAAG,cAAc,CAAC,CAAC,CAAC,CA0FpB"}