{"version": 3, "file": "useQuery.mjs", "sources": ["../../src/useQuery.ts"], "sourcesContent": ["'use client'\nimport { QueryObserver, parseQueryArgs } from '@tanstack/query-core'\nimport { useBaseQuery } from './useBaseQuery'\nimport type {\n  InitialDataFunction,\n  NonUndefinedGuard,\n  OmitKeyof,\n  QueryFunction,\n  QueryKey,\n} from '@tanstack/query-core'\nimport type {\n  DefinedUseQueryResult,\n  UseQueryOptions,\n  UseQueryResult,\n} from './types'\n\n// HOOK\nexport function useQuery<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  options: OmitKeyof<\n    UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    'initialData'\n  > & {\n    initialData:\n      | NonUndefinedGuard<TQueryFnData>\n      | (() => NonUndefinedGuard<TQueryFnData>)\n  },\n): DefinedUseQueryResult<TData, TError>\nexport function useQuery<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  options: OmitKeyof<\n    UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    'initialData'\n  > & {\n    initialData?:\n      | undefined\n      | InitialDataFunction<NonUndefinedGuard<TQueryFnData>>\n      | NonUndefinedGuard<TQueryFnData>\n  },\n): UseQueryResult<TData, TError>\nexport function useQuery<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  options: UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n): UseQueryResult<TData, TError>\n\n/** @deprecated */\nexport function useQuery<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  queryKey: TQueryKey,\n  options?: OmitKeyof<\n    UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    'queryKey' | 'initialData'\n  >,\n): UseQueryResult<TData, TError>\n/** @deprecated */\nexport function useQuery<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  queryKey: TQueryKey,\n  options?: OmitKeyof<\n    UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    'queryKey' | 'initialData'\n  > & { initialData: TQueryFnData | (() => TQueryFnData) },\n): DefinedUseQueryResult<TData, TError>\n/** @deprecated */\nexport function useQuery<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  queryKey: TQueryKey,\n  options?: OmitKeyof<\n    UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    'queryKey'\n  >,\n): UseQueryResult<TData, TError>\n/** @deprecated */\nexport function useQuery<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  queryKey: TQueryKey,\n  queryFn: QueryFunction<TQueryFnData, TQueryKey>,\n  options?: OmitKeyof<\n    UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    'queryKey' | 'queryFn' | 'initialData'\n  > & { initialData?: () => undefined },\n): UseQueryResult<TData, TError>\n/** @deprecated */\nexport function useQuery<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  queryKey: TQueryKey,\n  queryFn: QueryFunction<TQueryFnData, TQueryKey>,\n  options?: OmitKeyof<\n    UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    'queryKey' | 'queryFn' | 'initialData'\n  > & { initialData: TQueryFnData | (() => TQueryFnData) },\n): DefinedUseQueryResult<TData, TError>\n/** @deprecated */\nexport function useQuery<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  queryKey: TQueryKey,\n  queryFn: QueryFunction<TQueryFnData, TQueryKey>,\n  options?: OmitKeyof<\n    UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    'queryKey' | 'queryFn'\n  >,\n): UseQueryResult<TData, TError>\nexport function useQuery<\n  TQueryFnData,\n  TError,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  arg1: TQueryKey | UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n  arg2?:\n    | QueryFunction<TQueryFnData, TQueryKey>\n    | UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n  arg3?: UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n): UseQueryResult<TData, TError> {\n  const parsedOptions = parseQueryArgs(arg1, arg2, arg3)\n  return useBaseQuery(parsedOptions, QueryObserver)\n}\n"], "names": [], "mappings": ";;;;AA0IO;;AAaL;AACD;;"}