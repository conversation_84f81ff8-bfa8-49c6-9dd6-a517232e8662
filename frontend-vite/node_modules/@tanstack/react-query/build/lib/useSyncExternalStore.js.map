{"version": 3, "file": "useSyncExternalStore.js", "sources": ["../../src/useSyncExternalStore.ts"], "sourcesContent": ["'use client'\n// Temporary workaround due to an issue with react-native uSES - https://github.com/TanStack/query/pull/3601\nimport { useSyncExternalStore as uSES } from 'use-sync-external-store/shim/index.js'\n\nexport const useSyncExternalStore = uSES\n"], "names": [], "mappings": ";;;;;;;AAIO;;"}