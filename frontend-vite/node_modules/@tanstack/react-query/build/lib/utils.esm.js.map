{"version": 3, "file": "utils.esm.js", "sources": ["../../src/utils.ts"], "sourcesContent": ["export function shouldThrowError<T extends (...args: any[]) => boolean>(\n  _useErrorBoundary: boolean | T | undefined,\n  params: Parameters<T>,\n): boolean {\n  // Allow useErrorBoundary function to override throwing behavior on a per-error basis\n  if (typeof _useErrorBoundary === 'function') {\n    return _useErrorBoundary(...params)\n  }\n\n  return !!_useErrorBoundary\n}\n"], "names": ["shouldThrowError", "_useErrorBoundary", "params"], "mappings": "AAAO,SAASA,gBAAT,CACLC,iBADK,EAELC,MAFK,EAGI;AACT;AACA,EAAA,IAAI,OAAOD,iBAAP,KAA6B,UAAjC,EAA6C;AAC3C,IAAA,OAAOA,iBAAiB,CAAC,GAAGC,MAAJ,CAAxB,CAAA;AACD,GAAA;;EAED,OAAO,CAAC,CAACD,iBAAT,CAAA;AACD;;;;"}