{"version": 3, "file": "onlineManager.js", "sources": ["../../src/onlineManager.ts"], "sourcesContent": ["import { Subscribable } from './subscribable'\nimport { isServer } from './utils'\n\ntype SetupFn = (\n  setOnline: (online?: boolean) => void,\n) => (() => void) | undefined\n\nconst onlineEvents = ['online', 'offline'] as const\n\nexport class OnlineManager extends Subscribable {\n  private online?: boolean\n  private cleanup?: () => void\n\n  private setup: SetupFn\n\n  constructor() {\n    super()\n    this.setup = (onOnline) => {\n      // addEventListener does not exist in React Native, but window does\n      // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n      if (!isServer && window.addEventListener) {\n        const listener = () => onOnline()\n        // Listen to online\n        onlineEvents.forEach((event) => {\n          window.addEventListener(event, listener, false)\n        })\n\n        return () => {\n          // Be sure to unsubscribe if a new handler is set\n          onlineEvents.forEach((event) => {\n            window.removeEventListener(event, listener)\n          })\n        }\n      }\n\n      return\n    }\n  }\n\n  protected onSubscribe(): void {\n    if (!this.cleanup) {\n      this.setEventListener(this.setup)\n    }\n  }\n\n  protected onUnsubscribe() {\n    if (!this.hasListeners()) {\n      this.cleanup?.()\n      this.cleanup = undefined\n    }\n  }\n\n  setEventListener(setup: SetupFn): void {\n    this.setup = setup\n    this.cleanup?.()\n    this.cleanup = setup((online?: boolean) => {\n      if (typeof online === 'boolean') {\n        this.setOnline(online)\n      } else {\n        this.onOnline()\n      }\n    })\n  }\n\n  setOnline(online?: boolean): void {\n    const changed = this.online !== online\n\n    if (changed) {\n      this.online = online\n      this.onOnline()\n    }\n  }\n\n  onOnline(): void {\n    this.listeners.forEach(({ listener }) => {\n      listener()\n    })\n  }\n\n  isOnline(): boolean {\n    if (typeof this.online === 'boolean') {\n      return this.online\n    }\n\n    if (\n      typeof navigator === 'undefined' ||\n      typeof navigator.onLine === 'undefined'\n    ) {\n      return true\n    }\n\n    return navigator.onLine\n  }\n}\n\nexport const onlineManager = new OnlineManager()\n"], "names": ["onlineEvents", "OnlineManager", "Subscribable", "constructor", "setup", "onOnline", "isServer", "window", "addEventListener", "listener", "for<PERSON>ach", "event", "removeEventListener", "onSubscribe", "cleanup", "setEventListener", "onUnsubscribe", "hasListeners", "undefined", "online", "setOnline", "changed", "listeners", "isOnline", "navigator", "onLine", "onlineManager"], "mappings": ";;;;;;;AAOA,MAAMA,YAAY,GAAG,CAAC,QAAD,EAAW,SAAX,CAArB,CAAA;AAEO,MAAMC,aAAN,SAA4BC,yBAA5B,CAAyC;AAM9CC,EAAAA,WAAW,GAAG;AACZ,IAAA,KAAA,EAAA,CAAA;;IACA,IAAKC,CAAAA,KAAL,GAAcC,QAAD,IAAc;AACzB;AACA;AACA,MAAA,IAAI,CAACC,cAAD,IAAaC,MAAM,CAACC,gBAAxB,EAA0C;AACxC,QAAA,MAAMC,QAAQ,GAAG,MAAMJ,QAAQ,EAA/B,CADwC;;;AAGxCL,QAAAA,YAAY,CAACU,OAAb,CAAsBC,KAAD,IAAW;AAC9BJ,UAAAA,MAAM,CAACC,gBAAP,CAAwBG,KAAxB,EAA+BF,QAA/B,EAAyC,KAAzC,CAAA,CAAA;SADF,CAAA,CAAA;AAIA,QAAA,OAAO,MAAM;AACX;AACAT,UAAAA,YAAY,CAACU,OAAb,CAAsBC,KAAD,IAAW;AAC9BJ,YAAAA,MAAM,CAACK,mBAAP,CAA2BD,KAA3B,EAAkCF,QAAlC,CAAA,CAAA;WADF,CAAA,CAAA;SAFF,CAAA;AAMD,OAAA;;AAED,MAAA,OAAA;KAlBF,CAAA;AAoBD,GAAA;;AAESI,EAAAA,WAAW,GAAS;IAC5B,IAAI,CAAC,IAAKC,CAAAA,OAAV,EAAmB;MACjB,IAAKC,CAAAA,gBAAL,CAAsB,IAAA,CAAKX,KAA3B,CAAA,CAAA;AACD,KAAA;AACF,GAAA;;AAESY,EAAAA,aAAa,GAAG;AACxB,IAAA,IAAI,CAAC,IAAA,CAAKC,YAAL,EAAL,EAA0B;AAAA,MAAA,IAAA,aAAA,CAAA;;AACxB,MAAA,CAAA,aAAA,GAAA,IAAA,CAAKH,OAAL,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,aAAA,CAAA,IAAA,CAAA,IAAA,CAAA,CAAA;MACA,IAAKA,CAAAA,OAAL,GAAeI,SAAf,CAAA;AACD,KAAA;AACF,GAAA;;EAEDH,gBAAgB,CAACX,KAAD,EAAuB;AAAA,IAAA,IAAA,cAAA,CAAA;;IACrC,IAAKA,CAAAA,KAAL,GAAaA,KAAb,CAAA;AACA,IAAA,CAAA,cAAA,GAAA,IAAA,CAAKU,OAAL,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,cAAA,CAAA,IAAA,CAAA,IAAA,CAAA,CAAA;AACA,IAAA,IAAA,CAAKA,OAAL,GAAeV,KAAK,CAAEe,MAAD,IAAsB;AACzC,MAAA,IAAI,OAAOA,MAAP,KAAkB,SAAtB,EAAiC;QAC/B,IAAKC,CAAAA,SAAL,CAAeD,MAAf,CAAA,CAAA;AACD,OAFD,MAEO;AACL,QAAA,IAAA,CAAKd,QAAL,EAAA,CAAA;AACD,OAAA;AACF,KANmB,CAApB,CAAA;AAOD,GAAA;;EAEDe,SAAS,CAACD,MAAD,EAAyB;AAChC,IAAA,MAAME,OAAO,GAAG,IAAKF,CAAAA,MAAL,KAAgBA,MAAhC,CAAA;;AAEA,IAAA,IAAIE,OAAJ,EAAa;MACX,IAAKF,CAAAA,MAAL,GAAcA,MAAd,CAAA;AACA,MAAA,IAAA,CAAKd,QAAL,EAAA,CAAA;AACD,KAAA;AACF,GAAA;;AAEDA,EAAAA,QAAQ,GAAS;AACf,IAAA,IAAA,CAAKiB,SAAL,CAAeZ,OAAf,CAAuB,CAAC;AAAED,MAAAA,QAAAA;AAAF,KAAD,KAAkB;MACvCA,QAAQ,EAAA,CAAA;KADV,CAAA,CAAA;AAGD,GAAA;;AAEDc,EAAAA,QAAQ,GAAY;AAClB,IAAA,IAAI,OAAO,IAAA,CAAKJ,MAAZ,KAAuB,SAA3B,EAAsC;AACpC,MAAA,OAAO,KAAKA,MAAZ,CAAA;AACD,KAAA;;IAED,IACE,OAAOK,SAAP,KAAqB,WAArB,IACA,OAAOA,SAAS,CAACC,MAAjB,KAA4B,WAF9B,EAGE;AACA,MAAA,OAAO,IAAP,CAAA;AACD,KAAA;;IAED,OAAOD,SAAS,CAACC,MAAjB,CAAA;AACD,GAAA;;AAnF6C,CAAA;AAsFnCC,MAAAA,aAAa,GAAG,IAAIzB,aAAJ;;;;;"}