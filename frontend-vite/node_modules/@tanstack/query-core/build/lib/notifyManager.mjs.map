{"version": 3, "file": "notifyManager.mjs", "sources": ["../../src/notifyManager.ts"], "sourcesContent": ["import { scheduleMicrotask } from './utils'\n\n// TYPES\n\ntype NotifyCallback = () => void\n\ntype NotifyFunction = (callback: () => void) => void\n\ntype BatchNotifyFunction = (callback: () => void) => void\n\ntype BatchCallsCallback<T extends unknown[]> = (...args: T) => void\n\nexport function createNotifyManager() {\n  let queue: NotifyCallback[] = []\n  let transactions = 0\n  let notifyFn: NotifyFunction = (callback) => {\n    callback()\n  }\n  let batchNotifyFn: BatchNotifyFunction = (callback: () => void) => {\n    callback()\n  }\n\n  const batch = <T>(callback: () => T): T => {\n    let result\n    transactions++\n    try {\n      result = callback()\n    } finally {\n      transactions--\n      if (!transactions) {\n        flush()\n      }\n    }\n    return result\n  }\n\n  const schedule = (callback: NotifyCallback): void => {\n    if (transactions) {\n      queue.push(callback)\n    } else {\n      scheduleMicrotask(() => {\n        notifyFn(callback)\n      })\n    }\n  }\n\n  /**\n   * All calls to the wrapped function will be batched.\n   */\n  const batchCalls = <T extends unknown[]>(\n    callback: BatchCallsCallback<T>,\n  ): BatchCallsCallback<T> => {\n    return (...args) => {\n      schedule(() => {\n        callback(...args)\n      })\n    }\n  }\n\n  const flush = (): void => {\n    const originalQueue = queue\n    queue = []\n    if (originalQueue.length) {\n      scheduleMicrotask(() => {\n        batchNotifyFn(() => {\n          originalQueue.forEach((callback) => {\n            notifyFn(callback)\n          })\n        })\n      })\n    }\n  }\n\n  /**\n   * Use this method to set a custom notify function.\n   * This can be used to for example wrap notifications with `React.act` while running tests.\n   */\n  const setNotifyFunction = (fn: NotifyFunction) => {\n    notifyFn = fn\n  }\n\n  /**\n   * Use this method to set a custom function to batch notifications together into a single tick.\n   * By default React Query will use the batch function provided by ReactDOM or React Native.\n   */\n  const setBatchNotifyFunction = (fn: BatchNotifyFunction) => {\n    batchNotifyFn = fn\n  }\n\n  return {\n    batch,\n    batchCalls,\n    schedule,\n    setNotifyFunction,\n    setBatchNotifyFunction,\n  } as const\n}\n\n// SINGLETON\nexport const notifyManager = createNotifyManager()\n"], "names": ["createNotifyManager", "queue", "transactions", "notifyFn", "callback", "batchNotifyFn", "batch", "result", "flush", "schedule", "push", "scheduleMicrotask", "batchCalls", "args", "originalQueue", "length", "for<PERSON>ach", "setNotifyFunction", "fn", "setBatchNotifyFunction", "notify<PERSON><PERSON>ger"], "mappings": ";;AAYO,SAASA,mBAAT,GAA+B;EACpC,IAAIC,KAAuB,GAAG,EAA9B,CAAA;EACA,IAAIC,YAAY,GAAG,CAAnB,CAAA;;EACA,IAAIC,QAAwB,GAAIC,QAAD,IAAc;IAC3CA,QAAQ,EAAA,CAAA;GADV,CAAA;;EAGA,IAAIC,aAAkC,GAAID,QAAD,IAA0B;IACjEA,QAAQ,EAAA,CAAA;GADV,CAAA;;EAIA,MAAME,KAAK,GAAOF,QAAJ,IAA6B;AACzC,IAAA,IAAIG,MAAJ,CAAA;IACAL,YAAY,EAAA,CAAA;;IACZ,IAAI;MACFK,MAAM,GAAGH,QAAQ,EAAjB,CAAA;AACD,KAFD,SAEU;MACRF,YAAY,EAAA,CAAA;;MACZ,IAAI,CAACA,YAAL,EAAmB;QACjBM,KAAK,EAAA,CAAA;AACN,OAAA;AACF,KAAA;;AACD,IAAA,OAAOD,MAAP,CAAA;GAXF,CAAA;;EAcA,MAAME,QAAQ,GAAIL,QAAD,IAAoC;AACnD,IAAA,IAAIF,YAAJ,EAAkB;MAChBD,KAAK,CAACS,IAAN,CAAWN,QAAX,CAAA,CAAA;AACD,KAFD,MAEO;AACLO,MAAAA,iBAAiB,CAAC,MAAM;QACtBR,QAAQ,CAACC,QAAD,CAAR,CAAA;AACD,OAFgB,CAAjB,CAAA;AAGD,KAAA;GAPH,CAAA;AAUA;AACF;AACA;;;EACE,MAAMQ,UAAU,GACdR,QADiB,IAES;IAC1B,OAAO,CAAC,GAAGS,IAAJ,KAAa;AAClBJ,MAAAA,QAAQ,CAAC,MAAM;QACbL,QAAQ,CAAC,GAAGS,IAAJ,CAAR,CAAA;AACD,OAFO,CAAR,CAAA;KADF,CAAA;GAHF,CAAA;;EAUA,MAAML,KAAK,GAAG,MAAY;IACxB,MAAMM,aAAa,GAAGb,KAAtB,CAAA;AACAA,IAAAA,KAAK,GAAG,EAAR,CAAA;;IACA,IAAIa,aAAa,CAACC,MAAlB,EAA0B;AACxBJ,MAAAA,iBAAiB,CAAC,MAAM;AACtBN,QAAAA,aAAa,CAAC,MAAM;AAClBS,UAAAA,aAAa,CAACE,OAAd,CAAuBZ,QAAD,IAAc;YAClCD,QAAQ,CAACC,QAAD,CAAR,CAAA;WADF,CAAA,CAAA;AAGD,SAJY,CAAb,CAAA;AAKD,OANgB,CAAjB,CAAA;AAOD,KAAA;GAXH,CAAA;AAcA;AACF;AACA;AACA;;;EACE,MAAMa,iBAAiB,GAAIC,EAAD,IAAwB;AAChDf,IAAAA,QAAQ,GAAGe,EAAX,CAAA;GADF,CAAA;AAIA;AACF;AACA;AACA;;;EACE,MAAMC,sBAAsB,GAAID,EAAD,IAA6B;AAC1Db,IAAAA,aAAa,GAAGa,EAAhB,CAAA;GADF,CAAA;;EAIA,OAAO;IACLZ,KADK;IAELM,UAFK;IAGLH,QAHK;IAILQ,iBAJK;AAKLE,IAAAA,sBAAAA;GALF,CAAA;AAOD;;AAGYC,MAAAA,aAAa,GAAGpB,mBAAmB;;;;"}