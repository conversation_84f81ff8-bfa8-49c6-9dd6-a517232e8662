{"version": 3, "file": "utils.mjs", "sources": ["../../src/utils.ts"], "sourcesContent": ["import type { Mutation } from './mutation'\nimport type { Query } from './query'\nimport type {\n  FetchStatus,\n  MutationFunction,\n  MutationKey,\n  MutationOptions,\n  QueryFunction,\n  QueryKey,\n  QueryOptions,\n} from './types'\n\n// TYPES\n\nexport interface QueryFilters {\n  /**\n   * Filter to active queries, inactive queries or all queries\n   */\n  type?: QueryTypeFilter\n  /**\n   * Match query key exactly\n   */\n  exact?: boolean\n  /**\n   * Include queries matching this predicate function\n   */\n  predicate?: (query: Query) => boolean\n  /**\n   * Include queries matching this query key\n   */\n  queryKey?: QueryKey\n  /**\n   * Include or exclude stale queries\n   */\n  stale?: boolean\n  /**\n   * Include queries matching their fetchStatus\n   */\n  fetchStatus?: FetchStatus\n}\n\nexport interface MutationFilters {\n  /**\n   * Match mutation key exactly\n   */\n  exact?: boolean\n  /**\n   * Include mutations matching this predicate function\n   */\n  predicate?: (mutation: Mutation<any, any, any>) => boolean\n  /**\n   * Include mutations matching this mutation key\n   */\n  mutationKey?: Mutation<PERSON>ey\n  /**\n   * Include or exclude fetching mutations\n   */\n  fetching?: boolean\n}\n\nexport type DataUpdateFunction<TInput, TOutput> = (input: TInput) => TOutput\n\nexport type Updater<TInput, TOutput> =\n  | TOutput\n  | DataUpdateFunction<TInput, TOutput>\n\nexport type QueryTypeFilter = 'all' | 'active' | 'inactive'\n\n// UTILS\n\nexport const isServer = typeof window === 'undefined' || 'Deno' in window\n\nexport function noop(): undefined {\n  return undefined\n}\n\nexport function functionalUpdate<TInput, TOutput>(\n  updater: Updater<TInput, TOutput>,\n  input: TInput,\n): TOutput {\n  return typeof updater === 'function'\n    ? (updater as DataUpdateFunction<TInput, TOutput>)(input)\n    : updater\n}\n\nexport function isValidTimeout(value: unknown): value is number {\n  return typeof value === 'number' && value >= 0 && value !== Infinity\n}\n\nexport function difference<T>(array1: T[], array2: T[]): T[] {\n  return array1.filter((x) => !array2.includes(x))\n}\n\nexport function replaceAt<T>(array: T[], index: number, value: T): T[] {\n  const copy = array.slice(0)\n  copy[index] = value\n  return copy\n}\n\nexport function timeUntilStale(updatedAt: number, staleTime?: number): number {\n  return Math.max(updatedAt + (staleTime || 0) - Date.now(), 0)\n}\n\nexport function parseQueryArgs<\n  TOptions extends QueryOptions<any, any, any, TQueryKey>,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  arg1: TQueryKey | TOptions,\n  arg2?: QueryFunction<any, TQueryKey> | TOptions,\n  arg3?: TOptions,\n): TOptions {\n  if (!isQueryKey(arg1)) {\n    return arg1 as TOptions\n  }\n\n  if (typeof arg2 === 'function') {\n    return { ...arg3, queryKey: arg1, queryFn: arg2 } as TOptions\n  }\n\n  return { ...arg2, queryKey: arg1 } as TOptions\n}\n\nexport function parseMutationArgs<\n  TOptions extends MutationOptions<any, any, any, any>,\n>(\n  arg1: MutationKey | MutationFunction<any, any> | TOptions,\n  arg2?: MutationFunction<any, any> | TOptions,\n  arg3?: TOptions,\n): TOptions {\n  if (isQueryKey(arg1)) {\n    if (typeof arg2 === 'function') {\n      return { ...arg3, mutationKey: arg1, mutationFn: arg2 } as TOptions\n    }\n    return { ...arg2, mutationKey: arg1 } as TOptions\n  }\n\n  if (typeof arg1 === 'function') {\n    return { ...arg2, mutationFn: arg1 } as TOptions\n  }\n\n  return { ...arg1 } as TOptions\n}\n\nexport function parseFilterArgs<\n  TFilters extends QueryFilters,\n  TOptions = unknown,\n>(\n  arg1?: QueryKey | TFilters,\n  arg2?: TFilters | TOptions,\n  arg3?: TOptions,\n): [TFilters, TOptions | undefined] {\n  return (\n    isQueryKey(arg1) ? [{ ...arg2, queryKey: arg1 }, arg3] : [arg1 || {}, arg2]\n  ) as [TFilters, TOptions]\n}\n\nexport function parseMutationFilterArgs<\n  TFilters extends MutationFilters,\n  TOptions = unknown,\n>(\n  arg1?: QueryKey | TFilters,\n  arg2?: TFilters | TOptions,\n  arg3?: TOptions,\n): [TFilters, TOptions | undefined] {\n  return (\n    isQueryKey(arg1)\n      ? [{ ...arg2, mutationKey: arg1 }, arg3]\n      : [arg1 || {}, arg2]\n  ) as [TFilters, TOptions]\n}\n\nexport function matchQuery(\n  filters: QueryFilters,\n  query: Query<any, any, any, any>,\n): boolean {\n  const {\n    type = 'all',\n    exact,\n    fetchStatus,\n    predicate,\n    queryKey,\n    stale,\n  } = filters\n\n  if (isQueryKey(queryKey)) {\n    if (exact) {\n      if (query.queryHash !== hashQueryKeyByOptions(queryKey, query.options)) {\n        return false\n      }\n    } else if (!partialMatchKey(query.queryKey, queryKey)) {\n      return false\n    }\n  }\n\n  if (type !== 'all') {\n    const isActive = query.isActive()\n    if (type === 'active' && !isActive) {\n      return false\n    }\n    if (type === 'inactive' && isActive) {\n      return false\n    }\n  }\n\n  if (typeof stale === 'boolean' && query.isStale() !== stale) {\n    return false\n  }\n\n  if (\n    typeof fetchStatus !== 'undefined' &&\n    fetchStatus !== query.state.fetchStatus\n  ) {\n    return false\n  }\n\n  if (predicate && !predicate(query)) {\n    return false\n  }\n\n  return true\n}\n\nexport function matchMutation(\n  filters: MutationFilters,\n  mutation: Mutation<any, any>,\n): boolean {\n  const { exact, fetching, predicate, mutationKey } = filters\n  if (isQueryKey(mutationKey)) {\n    if (!mutation.options.mutationKey) {\n      return false\n    }\n    if (exact) {\n      if (\n        hashQueryKey(mutation.options.mutationKey) !== hashQueryKey(mutationKey)\n      ) {\n        return false\n      }\n    } else if (!partialMatchKey(mutation.options.mutationKey, mutationKey)) {\n      return false\n    }\n  }\n\n  if (\n    typeof fetching === 'boolean' &&\n    (mutation.state.status === 'loading') !== fetching\n  ) {\n    return false\n  }\n\n  if (predicate && !predicate(mutation)) {\n    return false\n  }\n\n  return true\n}\n\nexport function hashQueryKeyByOptions<TQueryKey extends QueryKey = QueryKey>(\n  queryKey: TQueryKey,\n  options?: QueryOptions<any, any, any, TQueryKey>,\n): string {\n  const hashFn = options?.queryKeyHashFn || hashQueryKey\n  return hashFn(queryKey)\n}\n\n/**\n * Default query keys hash function.\n * Hashes the value into a stable hash.\n */\nexport function hashQueryKey(queryKey: QueryKey): string {\n  return JSON.stringify(queryKey, (_, val) =>\n    isPlainObject(val)\n      ? Object.keys(val)\n          .sort()\n          .reduce((result, key) => {\n            result[key] = val[key]\n            return result\n          }, {} as any)\n      : val,\n  )\n}\n\n/**\n * Checks if key `b` partially matches with key `a`.\n */\nexport function partialMatchKey(a: QueryKey, b: QueryKey): boolean {\n  return partialDeepEqual(a, b)\n}\n\n/**\n * Checks if `b` partially matches with `a`.\n */\nexport function partialDeepEqual(a: any, b: any): boolean {\n  if (a === b) {\n    return true\n  }\n\n  if (typeof a !== typeof b) {\n    return false\n  }\n\n  if (a && b && typeof a === 'object' && typeof b === 'object') {\n    return !Object.keys(b).some((key) => !partialDeepEqual(a[key], b[key]))\n  }\n\n  return false\n}\n\n/**\n * This function returns `a` if `b` is deeply equal.\n * If not, it will replace any deeply equal children of `b` with those of `a`.\n * This can be used for structural sharing between JSON values for example.\n */\nexport function replaceEqualDeep<T>(a: unknown, b: T): T\nexport function replaceEqualDeep(a: any, b: any): any {\n  if (a === b) {\n    return a\n  }\n\n  const array = isPlainArray(a) && isPlainArray(b)\n\n  if (array || (isPlainObject(a) && isPlainObject(b))) {\n    const aSize = array ? a.length : Object.keys(a).length\n    const bItems = array ? b : Object.keys(b)\n    const bSize = bItems.length\n    const copy: any = array ? [] : {}\n\n    let equalItems = 0\n\n    for (let i = 0; i < bSize; i++) {\n      const key = array ? i : bItems[i]\n      copy[key] = replaceEqualDeep(a[key], b[key])\n      if (copy[key] === a[key]) {\n        equalItems++\n      }\n    }\n\n    return aSize === bSize && equalItems === aSize ? a : copy\n  }\n\n  return b\n}\n\n/**\n * Shallow compare objects. Only works with objects that always have the same properties.\n */\nexport function shallowEqualObjects<T>(a: T, b: T): boolean {\n  if ((a && !b) || (b && !a)) {\n    return false\n  }\n\n  for (const key in a) {\n    if (a[key] !== b[key]) {\n      return false\n    }\n  }\n\n  return true\n}\n\nexport function isPlainArray(value: unknown) {\n  return Array.isArray(value) && value.length === Object.keys(value).length\n}\n\n// Copied from: https://github.com/jonschlinkert/is-plain-object\nexport function isPlainObject(o: any): o is Object {\n  if (!hasObjectPrototype(o)) {\n    return false\n  }\n\n  // If has modified constructor\n  const ctor = o.constructor\n  if (typeof ctor === 'undefined') {\n    return true\n  }\n\n  // If has modified prototype\n  const prot = ctor.prototype\n  if (!hasObjectPrototype(prot)) {\n    return false\n  }\n\n  // If constructor does not have an Object-specific method\n  if (!prot.hasOwnProperty('isPrototypeOf')) {\n    return false\n  }\n\n  // Most likely a plain Object\n  return true\n}\n\nfunction hasObjectPrototype(o: any): boolean {\n  return Object.prototype.toString.call(o) === '[object Object]'\n}\n\nexport function isQueryKey(value: unknown): value is QueryKey {\n  return Array.isArray(value)\n}\n\nexport function isError(value: any): value is Error {\n  return value instanceof Error\n}\n\nexport function sleep(timeout: number): Promise<void> {\n  return new Promise((resolve) => {\n    setTimeout(resolve, timeout)\n  })\n}\n\n/**\n * Schedules a microtask.\n * This can be useful to schedule state updates after rendering.\n */\nexport function scheduleMicrotask(callback: () => void) {\n  sleep(0).then(callback)\n}\n\nexport function getAbortController(): AbortController | undefined {\n  if (typeof AbortController === 'function') {\n    return new AbortController()\n  }\n  return\n}\n\nexport function replaceData<\n  TData,\n  TOptions extends QueryOptions<any, any, any, any>,\n>(prevData: TData | undefined, data: TData, options: TOptions): TData {\n  // Use prev data if an isDataEqual function is defined and returns `true`\n  if (options.isDataEqual?.(prevData, data)) {\n    return prevData as TData\n  } else if (typeof options.structuralSharing === 'function') {\n    return options.structuralSharing(prevData, data)\n  } else if (options.structuralSharing !== false) {\n    // Structurally share data between prev and new data if needed\n    return replaceEqualDeep(prevData, data)\n  }\n  return data\n}\n"], "names": ["isServer", "window", "noop", "undefined", "functionalUpdate", "updater", "input", "isValidTimeout", "value", "Infinity", "difference", "array1", "array2", "filter", "x", "includes", "replaceAt", "array", "index", "copy", "slice", "timeUntilStale", "updatedAt", "staleTime", "Math", "max", "Date", "now", "parse<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "arg1", "arg2", "arg3", "is<PERSON>uery<PERSON>ey", "query<PERSON><PERSON>", "queryFn", "parseMutationArgs", "<PERSON><PERSON><PERSON>", "mutationFn", "parseFilter<PERSON><PERSON>s", "parseMutationFilterArgs", "matchQuery", "filters", "query", "type", "exact", "fetchStatus", "predicate", "stale", "queryHash", "hashQueryKeyByOptions", "options", "partialMatchKey", "isActive", "isStale", "state", "matchMutation", "mutation", "fetching", "hashQuery<PERSON>ey", "status", "hashFn", "queryKeyHashFn", "JSON", "stringify", "_", "val", "isPlainObject", "Object", "keys", "sort", "reduce", "result", "key", "a", "b", "partialDeepEqual", "some", "replaceEqualDeep", "is<PERSON><PERSON>A<PERSON>y", "aSize", "length", "bItems", "bSize", "equalItems", "i", "shallowEqualObjects", "Array", "isArray", "o", "hasObjectPrototype", "ctor", "constructor", "prot", "prototype", "hasOwnProperty", "toString", "call", "isError", "Error", "sleep", "timeout", "Promise", "resolve", "setTimeout", "scheduleMicrotask", "callback", "then", "getAbortController", "AbortController", "replaceData", "prevData", "data", "isDataEqual", "structuralSharing"], "mappings": "AAYA;AAwDA;AAEO,MAAMA,QAAQ,GAAG,OAAOC,MAAP,KAAkB,WAAlB,IAAiC,MAAA,IAAUA,OAA5D;AAEA,SAASC,IAAT,GAA2B;AAChC,EAAA,OAAOC,SAAP,CAAA;AACD,CAAA;AAEM,SAASC,gBAAT,CACLC,OADK,EAELC,KAFK,EAGI;EACT,OAAO,OAAOD,OAAP,KAAmB,UAAnB,GACFA,OAAD,CAAiDC,KAAjD,CADG,GAEHD,OAFJ,CAAA;AAGD,CAAA;AAEM,SAASE,cAAT,CAAwBC,KAAxB,EAAyD;EAC9D,OAAO,OAAOA,KAAP,KAAiB,QAAjB,IAA6BA,KAAK,IAAI,CAAtC,IAA2CA,KAAK,KAAKC,QAA5D,CAAA;AACD,CAAA;AAEM,SAASC,UAAT,CAAuBC,MAAvB,EAAoCC,MAApC,EAAsD;AAC3D,EAAA,OAAOD,MAAM,CAACE,MAAP,CAAeC,CAAD,IAAO,CAACF,MAAM,CAACG,QAAP,CAAgBD,CAAhB,CAAtB,CAAP,CAAA;AACD,CAAA;AAEM,SAASE,SAAT,CAAsBC,KAAtB,EAAkCC,KAAlC,EAAiDV,KAAjD,EAAgE;AACrE,EAAA,MAAMW,IAAI,GAAGF,KAAK,CAACG,KAAN,CAAY,CAAZ,CAAb,CAAA;AACAD,EAAAA,IAAI,CAACD,KAAD,CAAJ,GAAcV,KAAd,CAAA;AACA,EAAA,OAAOW,IAAP,CAAA;AACD,CAAA;AAEM,SAASE,cAAT,CAAwBC,SAAxB,EAA2CC,SAA3C,EAAuE;AAC5E,EAAA,OAAOC,IAAI,CAACC,GAAL,CAASH,SAAS,IAAIC,SAAS,IAAI,CAAjB,CAAT,GAA+BG,IAAI,CAACC,GAAL,EAAxC,EAAoD,CAApD,CAAP,CAAA;AACD,CAAA;AAEM,SAASC,cAAT,CAILC,IAJK,EAKLC,IALK,EAMLC,IANK,EAOK;AACV,EAAA,IAAI,CAACC,UAAU,CAACH,IAAD,CAAf,EAAuB;AACrB,IAAA,OAAOA,IAAP,CAAA;AACD,GAAA;;AAED,EAAA,IAAI,OAAOC,IAAP,KAAgB,UAApB,EAAgC;IAC9B,OAAO,EAAE,GAAGC,IAAL;AAAWE,MAAAA,QAAQ,EAAEJ,IAArB;AAA2BK,MAAAA,OAAO,EAAEJ,IAAAA;KAA3C,CAAA;AACD,GAAA;;EAED,OAAO,EAAE,GAAGA,IAAL;AAAWG,IAAAA,QAAQ,EAAEJ,IAAAA;GAA5B,CAAA;AACD,CAAA;AAEM,SAASM,iBAAT,CAGLN,IAHK,EAILC,IAJK,EAKLC,IALK,EAMK;AACV,EAAA,IAAIC,UAAU,CAACH,IAAD,CAAd,EAAsB;AACpB,IAAA,IAAI,OAAOC,IAAP,KAAgB,UAApB,EAAgC;MAC9B,OAAO,EAAE,GAAGC,IAAL;AAAWK,QAAAA,WAAW,EAAEP,IAAxB;AAA8BQ,QAAAA,UAAU,EAAEP,IAAAA;OAAjD,CAAA;AACD,KAAA;;IACD,OAAO,EAAE,GAAGA,IAAL;AAAWM,MAAAA,WAAW,EAAEP,IAAAA;KAA/B,CAAA;AACD,GAAA;;AAED,EAAA,IAAI,OAAOA,IAAP,KAAgB,UAApB,EAAgC;IAC9B,OAAO,EAAE,GAAGC,IAAL;AAAWO,MAAAA,UAAU,EAAER,IAAAA;KAA9B,CAAA;AACD,GAAA;;AAED,EAAA,OAAO,EAAE,GAAGA,IAAAA;GAAZ,CAAA;AACD,CAAA;AAEM,SAASS,eAAT,CAILT,IAJK,EAKLC,IALK,EAMLC,IANK,EAO6B;EAClC,OACEC,UAAU,CAACH,IAAD,CAAV,GAAmB,CAAC,EAAE,GAAGC,IAAL;AAAWG,IAAAA,QAAQ,EAAEJ,IAAAA;GAAtB,EAA8BE,IAA9B,CAAnB,GAAyD,CAACF,IAAI,IAAI,EAAT,EAAaC,IAAb,CAD3D,CAAA;AAGD,CAAA;AAEM,SAASS,uBAAT,CAILV,IAJK,EAKLC,IALK,EAMLC,IANK,EAO6B;EAClC,OACEC,UAAU,CAACH,IAAD,CAAV,GACI,CAAC,EAAE,GAAGC,IAAL;AAAWM,IAAAA,WAAW,EAAEP,IAAAA;GAAzB,EAAiCE,IAAjC,CADJ,GAEI,CAACF,IAAI,IAAI,EAAT,EAAaC,IAAb,CAHN,CAAA;AAKD,CAAA;AAEM,SAASU,UAAT,CACLC,OADK,EAELC,KAFK,EAGI;EACT,MAAM;AACJC,IAAAA,IAAI,GAAG,KADH;IAEJC,KAFI;IAGJC,WAHI;IAIJC,SAJI;IAKJb,QALI;AAMJc,IAAAA,KAAAA;AANI,GAAA,GAOFN,OAPJ,CAAA;;AASA,EAAA,IAAIT,UAAU,CAACC,QAAD,CAAd,EAA0B;AACxB,IAAA,IAAIW,KAAJ,EAAW;AACT,MAAA,IAAIF,KAAK,CAACM,SAAN,KAAoBC,qBAAqB,CAAChB,QAAD,EAAWS,KAAK,CAACQ,OAAjB,CAA7C,EAAwE;AACtE,QAAA,OAAO,KAAP,CAAA;AACD,OAAA;KAHH,MAIO,IAAI,CAACC,eAAe,CAACT,KAAK,CAACT,QAAP,EAAiBA,QAAjB,CAApB,EAAgD;AACrD,MAAA,OAAO,KAAP,CAAA;AACD,KAAA;AACF,GAAA;;EAED,IAAIU,IAAI,KAAK,KAAb,EAAoB;AAClB,IAAA,MAAMS,QAAQ,GAAGV,KAAK,CAACU,QAAN,EAAjB,CAAA;;AACA,IAAA,IAAIT,IAAI,KAAK,QAAT,IAAqB,CAACS,QAA1B,EAAoC;AAClC,MAAA,OAAO,KAAP,CAAA;AACD,KAAA;;AACD,IAAA,IAAIT,IAAI,KAAK,UAAT,IAAuBS,QAA3B,EAAqC;AACnC,MAAA,OAAO,KAAP,CAAA;AACD,KAAA;AACF,GAAA;;EAED,IAAI,OAAOL,KAAP,KAAiB,SAAjB,IAA8BL,KAAK,CAACW,OAAN,EAAoBN,KAAAA,KAAtD,EAA6D;AAC3D,IAAA,OAAO,KAAP,CAAA;AACD,GAAA;;AAED,EAAA,IACE,OAAOF,WAAP,KAAuB,WAAvB,IACAA,WAAW,KAAKH,KAAK,CAACY,KAAN,CAAYT,WAF9B,EAGE;AACA,IAAA,OAAO,KAAP,CAAA;AACD,GAAA;;AAED,EAAA,IAAIC,SAAS,IAAI,CAACA,SAAS,CAACJ,KAAD,CAA3B,EAAoC;AAClC,IAAA,OAAO,KAAP,CAAA;AACD,GAAA;;AAED,EAAA,OAAO,IAAP,CAAA;AACD,CAAA;AAEM,SAASa,aAAT,CACLd,OADK,EAELe,QAFK,EAGI;EACT,MAAM;IAAEZ,KAAF;IAASa,QAAT;IAAmBX,SAAnB;AAA8BV,IAAAA,WAAAA;AAA9B,GAAA,GAA8CK,OAApD,CAAA;;AACA,EAAA,IAAIT,UAAU,CAACI,WAAD,CAAd,EAA6B;AAC3B,IAAA,IAAI,CAACoB,QAAQ,CAACN,OAAT,CAAiBd,WAAtB,EAAmC;AACjC,MAAA,OAAO,KAAP,CAAA;AACD,KAAA;;AACD,IAAA,IAAIQ,KAAJ,EAAW;AACT,MAAA,IACEc,YAAY,CAACF,QAAQ,CAACN,OAAT,CAAiBd,WAAlB,CAAZ,KAA+CsB,YAAY,CAACtB,WAAD,CAD7D,EAEE;AACA,QAAA,OAAO,KAAP,CAAA;AACD,OAAA;AACF,KAND,MAMO,IAAI,CAACe,eAAe,CAACK,QAAQ,CAACN,OAAT,CAAiBd,WAAlB,EAA+BA,WAA/B,CAApB,EAAiE;AACtE,MAAA,OAAO,KAAP,CAAA;AACD,KAAA;AACF,GAAA;;AAED,EAAA,IACE,OAAOqB,QAAP,KAAoB,SAApB,IACCD,QAAQ,CAACF,KAAT,CAAeK,MAAf,KAA0B,SAA3B,KAA0CF,QAF5C,EAGE;AACA,IAAA,OAAO,KAAP,CAAA;AACD,GAAA;;AAED,EAAA,IAAIX,SAAS,IAAI,CAACA,SAAS,CAACU,QAAD,CAA3B,EAAuC;AACrC,IAAA,OAAO,KAAP,CAAA;AACD,GAAA;;AAED,EAAA,OAAO,IAAP,CAAA;AACD,CAAA;AAEM,SAASP,qBAAT,CACLhB,QADK,EAELiB,OAFK,EAGG;EACR,MAAMU,MAAM,GAAG,CAAAV,OAAO,IAAA,IAAP,YAAAA,OAAO,CAAEW,cAAT,KAA2BH,YAA1C,CAAA;EACA,OAAOE,MAAM,CAAC3B,QAAD,CAAb,CAAA;AACD,CAAA;AAED;AACA;AACA;AACA;;AACO,SAASyB,YAAT,CAAsBzB,QAAtB,EAAkD;AACvD,EAAA,OAAO6B,IAAI,CAACC,SAAL,CAAe9B,QAAf,EAAyB,CAAC+B,CAAD,EAAIC,GAAJ,KAC9BC,aAAa,CAACD,GAAD,CAAb,GACIE,MAAM,CAACC,IAAP,CAAYH,GAAZ,CACGI,CAAAA,IADH,EAEGC,CAAAA,MAFH,CAEU,CAACC,MAAD,EAASC,GAAT,KAAiB;AACvBD,IAAAA,MAAM,CAACC,GAAD,CAAN,GAAcP,GAAG,CAACO,GAAD,CAAjB,CAAA;AACA,IAAA,OAAOD,MAAP,CAAA;AACD,GALH,EAKK,EALL,CADJ,GAOIN,GARC,CAAP,CAAA;AAUD,CAAA;AAED;AACA;AACA;;AACO,SAASd,eAAT,CAAyBsB,CAAzB,EAAsCC,CAAtC,EAA4D;AACjE,EAAA,OAAOC,gBAAgB,CAACF,CAAD,EAAIC,CAAJ,CAAvB,CAAA;AACD,CAAA;AAED;AACA;AACA;;AACO,SAASC,gBAAT,CAA0BF,CAA1B,EAAkCC,CAAlC,EAAmD;EACxD,IAAID,CAAC,KAAKC,CAAV,EAAa;AACX,IAAA,OAAO,IAAP,CAAA;AACD,GAAA;;AAED,EAAA,IAAI,OAAOD,CAAP,KAAa,OAAOC,CAAxB,EAA2B;AACzB,IAAA,OAAO,KAAP,CAAA;AACD,GAAA;;AAED,EAAA,IAAID,CAAC,IAAIC,CAAL,IAAU,OAAOD,CAAP,KAAa,QAAvB,IAAmC,OAAOC,CAAP,KAAa,QAApD,EAA8D;IAC5D,OAAO,CAACP,MAAM,CAACC,IAAP,CAAYM,CAAZ,CAAeE,CAAAA,IAAf,CAAqBJ,GAAD,IAAS,CAACG,gBAAgB,CAACF,CAAC,CAACD,GAAD,CAAF,EAASE,CAAC,CAACF,GAAD,CAAV,CAA9C,CAAR,CAAA;AACD,GAAA;;AAED,EAAA,OAAO,KAAP,CAAA;AACD,CAAA;AAED;AACA;AACA;AACA;AACA;;AAEO,SAASK,gBAAT,CAA0BJ,CAA1B,EAAkCC,CAAlC,EAA+C;EACpD,IAAID,CAAC,KAAKC,CAAV,EAAa;AACX,IAAA,OAAOD,CAAP,CAAA;AACD,GAAA;;EAED,MAAMxD,KAAK,GAAG6D,YAAY,CAACL,CAAD,CAAZ,IAAmBK,YAAY,CAACJ,CAAD,CAA7C,CAAA;;EAEA,IAAIzD,KAAK,IAAKiD,aAAa,CAACO,CAAD,CAAb,IAAoBP,aAAa,CAACQ,CAAD,CAA/C,EAAqD;AACnD,IAAA,MAAMK,KAAK,GAAG9D,KAAK,GAAGwD,CAAC,CAACO,MAAL,GAAcb,MAAM,CAACC,IAAP,CAAYK,CAAZ,EAAeO,MAAhD,CAAA;IACA,MAAMC,MAAM,GAAGhE,KAAK,GAAGyD,CAAH,GAAOP,MAAM,CAACC,IAAP,CAAYM,CAAZ,CAA3B,CAAA;AACA,IAAA,MAAMQ,KAAK,GAAGD,MAAM,CAACD,MAArB,CAAA;AACA,IAAA,MAAM7D,IAAS,GAAGF,KAAK,GAAG,EAAH,GAAQ,EAA/B,CAAA;IAEA,IAAIkE,UAAU,GAAG,CAAjB,CAAA;;IAEA,KAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGF,KAApB,EAA2BE,CAAC,EAA5B,EAAgC;MAC9B,MAAMZ,GAAG,GAAGvD,KAAK,GAAGmE,CAAH,GAAOH,MAAM,CAACG,CAAD,CAA9B,CAAA;AACAjE,MAAAA,IAAI,CAACqD,GAAD,CAAJ,GAAYK,gBAAgB,CAACJ,CAAC,CAACD,GAAD,CAAF,EAASE,CAAC,CAACF,GAAD,CAAV,CAA5B,CAAA;;MACA,IAAIrD,IAAI,CAACqD,GAAD,CAAJ,KAAcC,CAAC,CAACD,GAAD,CAAnB,EAA0B;QACxBW,UAAU,EAAA,CAAA;AACX,OAAA;AACF,KAAA;;IAED,OAAOJ,KAAK,KAAKG,KAAV,IAAmBC,UAAU,KAAKJ,KAAlC,GAA0CN,CAA1C,GAA8CtD,IAArD,CAAA;AACD,GAAA;;AAED,EAAA,OAAOuD,CAAP,CAAA;AACD,CAAA;AAED;AACA;AACA;;AACO,SAASW,mBAAT,CAAgCZ,CAAhC,EAAsCC,CAAtC,EAAqD;EAC1D,IAAKD,CAAC,IAAI,CAACC,CAAP,IAAcA,CAAC,IAAI,CAACD,CAAxB,EAA4B;AAC1B,IAAA,OAAO,KAAP,CAAA;AACD,GAAA;;AAED,EAAA,KAAK,MAAMD,GAAX,IAAkBC,CAAlB,EAAqB;IACnB,IAAIA,CAAC,CAACD,GAAD,CAAD,KAAWE,CAAC,CAACF,GAAD,CAAhB,EAAuB;AACrB,MAAA,OAAO,KAAP,CAAA;AACD,KAAA;AACF,GAAA;;AAED,EAAA,OAAO,IAAP,CAAA;AACD,CAAA;AAEM,SAASM,YAAT,CAAsBtE,KAAtB,EAAsC;AAC3C,EAAA,OAAO8E,KAAK,CAACC,OAAN,CAAc/E,KAAd,KAAwBA,KAAK,CAACwE,MAAN,KAAiBb,MAAM,CAACC,IAAP,CAAY5D,KAAZ,EAAmBwE,MAAnE,CAAA;AACD;;AAGM,SAASd,aAAT,CAAuBsB,CAAvB,EAA4C;AACjD,EAAA,IAAI,CAACC,kBAAkB,CAACD,CAAD,CAAvB,EAA4B;AAC1B,IAAA,OAAO,KAAP,CAAA;AACD,GAHgD;;;AAMjD,EAAA,MAAME,IAAI,GAAGF,CAAC,CAACG,WAAf,CAAA;;AACA,EAAA,IAAI,OAAOD,IAAP,KAAgB,WAApB,EAAiC;AAC/B,IAAA,OAAO,IAAP,CAAA;AACD,GATgD;;;AAYjD,EAAA,MAAME,IAAI,GAAGF,IAAI,CAACG,SAAlB,CAAA;;AACA,EAAA,IAAI,CAACJ,kBAAkB,CAACG,IAAD,CAAvB,EAA+B;AAC7B,IAAA,OAAO,KAAP,CAAA;AACD,GAfgD;;;AAkBjD,EAAA,IAAI,CAACA,IAAI,CAACE,cAAL,CAAoB,eAApB,CAAL,EAA2C;AACzC,IAAA,OAAO,KAAP,CAAA;AACD,GApBgD;;;AAuBjD,EAAA,OAAO,IAAP,CAAA;AACD,CAAA;;AAED,SAASL,kBAAT,CAA4BD,CAA5B,EAA6C;EAC3C,OAAOrB,MAAM,CAAC0B,SAAP,CAAiBE,QAAjB,CAA0BC,IAA1B,CAA+BR,CAA/B,CAAA,KAAsC,iBAA7C,CAAA;AACD,CAAA;;AAEM,SAASxD,UAAT,CAAoBxB,KAApB,EAAuD;AAC5D,EAAA,OAAO8E,KAAK,CAACC,OAAN,CAAc/E,KAAd,CAAP,CAAA;AACD,CAAA;AAEM,SAASyF,OAAT,CAAiBzF,KAAjB,EAA6C;EAClD,OAAOA,KAAK,YAAY0F,KAAxB,CAAA;AACD,CAAA;AAEM,SAASC,KAAT,CAAeC,OAAf,EAA+C;AACpD,EAAA,OAAO,IAAIC,OAAJ,CAAaC,OAAD,IAAa;AAC9BC,IAAAA,UAAU,CAACD,OAAD,EAAUF,OAAV,CAAV,CAAA;AACD,GAFM,CAAP,CAAA;AAGD,CAAA;AAED;AACA;AACA;AACA;;AACO,SAASI,iBAAT,CAA2BC,QAA3B,EAAiD;AACtDN,EAAAA,KAAK,CAAC,CAAD,CAAL,CAASO,IAAT,CAAcD,QAAd,CAAA,CAAA;AACD,CAAA;AAEM,SAASE,kBAAT,GAA2D;AAChE,EAAA,IAAI,OAAOC,eAAP,KAA2B,UAA/B,EAA2C;IACzC,OAAO,IAAIA,eAAJ,EAAP,CAAA;AACD,GAAA;;AACD,EAAA,OAAA;AACD,CAAA;AAEM,SAASC,WAAT,CAGLC,QAHK,EAGwBC,IAHxB,EAGqC7D,OAHrC,EAG+D;AACpE;AACA,EAAA,IAAIA,OAAO,CAAC8D,WAAZ,IAAA,IAAA,IAAI9D,OAAO,CAAC8D,WAAR,CAAsBF,QAAtB,EAAgCC,IAAhC,CAAJ,EAA2C;AACzC,IAAA,OAAOD,QAAP,CAAA;GADF,MAEO,IAAI,OAAO5D,OAAO,CAAC+D,iBAAf,KAAqC,UAAzC,EAAqD;AAC1D,IAAA,OAAO/D,OAAO,CAAC+D,iBAAR,CAA0BH,QAA1B,EAAoCC,IAApC,CAAP,CAAA;AACD,GAFM,MAEA,IAAI7D,OAAO,CAAC+D,iBAAR,KAA8B,KAAlC,EAAyC;AAC9C;AACA,IAAA,OAAOpC,gBAAgB,CAACiC,QAAD,EAAWC,IAAX,CAAvB,CAAA;AACD,GAAA;;AACD,EAAA,OAAOA,IAAP,CAAA;AACD;;;;"}