{"version": 3, "file": "queryClient.esm.js", "sources": ["../../src/queryClient.ts"], "sourcesContent": ["import {\n  functionalUpdate,\n  hashQueryKey,\n  hashQueryKeyByOptions,\n  noop,\n  parseFilterArgs,\n  parseQueryArgs,\n  partialMatchKey,\n} from './utils'\nimport { QueryCache } from './queryCache'\nimport { MutationCache } from './mutationCache'\nimport { focusManager } from './focusManager'\nimport { onlineManager } from './onlineManager'\nimport { notifyManager } from './notifyManager'\nimport { infiniteQueryBehavior } from './infiniteQueryBehavior'\nimport { defaultLogger } from './logger'\nimport type { OmitKeyof } from '@tanstack/query-core'\nimport type { CancelOptions, DefaultedQueryObserverOptions } from './types'\nimport type { Logger } from './logger'\nimport type { QueryState } from './query'\nimport type {\n  DefaultOptions,\n  FetchInfiniteQueryOptions,\n  FetchQueryOptions,\n  InfiniteData,\n  InvalidateOptions,\n  InvalidateQueryFilters,\n  MutationKey,\n  MutationObserverOptions,\n  MutationOptions,\n  QueryClientConfig,\n  QueryFunction,\n  QueryKey,\n  QueryObserverOptions,\n  QueryOptions,\n  RefetchOptions,\n  RefetchQueryFilters,\n  ResetOptions,\n  ResetQueryFilters,\n  SetDataOptions,\n  WithRequired,\n} from './types'\nimport type { MutationFilters, QueryFilters, Updater } from './utils'\n\n// TYPES\n\ninterface QueryDefaults {\n  queryKey: QueryKey\n  defaultOptions: QueryOptions<any, any, any>\n}\n\ninterface MutationDefaults {\n  mutationKey: MutationKey\n  defaultOptions: MutationOptions<any, any, any, any>\n}\n\n// CLASS\n\nexport class QueryClient {\n  private queryCache: QueryCache\n  private mutationCache: MutationCache\n  private logger: Logger\n  private defaultOptions: DefaultOptions\n  private queryDefaults: QueryDefaults[]\n  private mutationDefaults: MutationDefaults[]\n  private mountCount: number\n  private unsubscribeFocus?: () => void\n  private unsubscribeOnline?: () => void\n\n  constructor(config: QueryClientConfig = {}) {\n    this.queryCache = config.queryCache || new QueryCache()\n    this.mutationCache = config.mutationCache || new MutationCache()\n    this.logger = config.logger || defaultLogger\n    this.defaultOptions = config.defaultOptions || {}\n    this.queryDefaults = []\n    this.mutationDefaults = []\n    this.mountCount = 0\n\n    if (process.env.NODE_ENV !== 'production' && config.logger) {\n      this.logger.error(\n        `Passing a custom logger has been deprecated and will be removed in the next major version.`,\n      )\n    }\n  }\n\n  mount(): void {\n    this.mountCount++\n    if (this.mountCount !== 1) return\n\n    this.unsubscribeFocus = focusManager.subscribe(() => {\n      if (focusManager.isFocused()) {\n        this.resumePausedMutations()\n        this.queryCache.onFocus()\n      }\n    })\n    this.unsubscribeOnline = onlineManager.subscribe(() => {\n      if (onlineManager.isOnline()) {\n        this.resumePausedMutations()\n        this.queryCache.onOnline()\n      }\n    })\n  }\n\n  unmount(): void {\n    this.mountCount--\n    if (this.mountCount !== 0) return\n\n    this.unsubscribeFocus?.()\n    this.unsubscribeFocus = undefined\n\n    this.unsubscribeOnline?.()\n    this.unsubscribeOnline = undefined\n  }\n\n  isFetching(filters?: QueryFilters): number\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  isFetching(\n    queryKey?: QueryKey,\n    filters?: OmitKeyof<QueryFilters, 'queryKey'>,\n  ): number\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  isFetching(arg1?: QueryKey | QueryFilters, arg2?: QueryFilters): number {\n    const [filters] = parseFilterArgs(arg1, arg2)\n    filters.fetchStatus = 'fetching'\n    return this.queryCache.findAll(filters).length\n  }\n\n  isMutating(filters?: MutationFilters): number {\n    return this.mutationCache.findAll({ ...filters, fetching: true }).length\n  }\n\n  getQueryData<TQueryFnData = unknown>(\n    queryKey: QueryKey,\n  ): TQueryFnData | undefined\n  /**\n   * @deprecated This method will accept only queryKey in the next major version.\n   */\n  getQueryData<TQueryFnData = unknown>(\n    queryKey: QueryKey,\n    filters: OmitKeyof<QueryFilters, 'queryKey'>,\n  ): TQueryFnData | undefined\n  /**\n   * @deprecated This method will accept only queryKey in the next major version.\n   */\n  getQueryData<TQueryFnData = unknown>(\n    queryKey: QueryKey,\n    filters?: OmitKeyof<QueryFilters, 'queryKey'>,\n  ): TQueryFnData | undefined {\n    return this.queryCache.find<TQueryFnData>(queryKey, filters)?.state.data\n  }\n\n  ensureQueryData<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    options: WithRequired<\n      FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n      'queryKey'\n    >,\n  ): Promise<TData>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  ensureQueryData<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    queryKey: TQueryKey,\n    options?: OmitKeyof<\n      FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n      'queryKey'\n    >,\n  ): Promise<TData>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  ensureQueryData<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    queryKey: TQueryKey,\n    queryFn: QueryFunction<TQueryFnData, TQueryKey>,\n    options?: OmitKeyof<\n      FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n      'queryKey' | 'queryFn'\n    >,\n  ): Promise<TData>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  ensureQueryData<\n    TQueryFnData,\n    TError,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    arg1:\n      | TQueryKey\n      | WithRequired<\n          FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n          'queryKey'\n        >,\n    arg2?:\n      | QueryFunction<TQueryFnData, TQueryKey>\n      | FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    arg3?: FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n  ): Promise<TData> {\n    const parsedOptions = parseQueryArgs(arg1, arg2, arg3)\n    const cachedData = this.getQueryData<TData>(parsedOptions.queryKey!)\n\n    return cachedData\n      ? Promise.resolve(cachedData)\n      : this.fetchQuery(parsedOptions)\n  }\n\n  getQueriesData<TQueryFnData = unknown>(\n    filters: QueryFilters,\n  ): [QueryKey, TQueryFnData | undefined][]\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  getQueriesData<TQueryFnData = unknown>(\n    queryKey: QueryKey,\n  ): [QueryKey, TQueryFnData | undefined][]\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  getQueriesData<TQueryFnData = unknown>(\n    queryKeyOrFilters: QueryKey | QueryFilters,\n  ): [QueryKey, TQueryFnData | undefined][] {\n    return this.getQueryCache()\n      .findAll(queryKeyOrFilters)\n      .map(({ queryKey, state }) => {\n        const data = state.data as TQueryFnData | undefined\n        return [queryKey, data]\n      })\n  }\n\n  setQueryData<TQueryFnData>(\n    queryKey: QueryKey,\n    updater: Updater<TQueryFnData | undefined, TQueryFnData | undefined>,\n    options?: SetDataOptions,\n  ): TQueryFnData | undefined {\n    const query = this.queryCache.find<TQueryFnData>(queryKey)\n    const prevData = query?.state.data\n    const data = functionalUpdate(updater, prevData)\n\n    if (typeof data === 'undefined') {\n      return undefined\n    }\n\n    const parsedOptions = parseQueryArgs(queryKey)\n    const defaultedOptions = this.defaultQueryOptions(parsedOptions)\n    return this.queryCache\n      .build(this, defaultedOptions)\n      .setData(data, { ...options, manual: true })\n  }\n\n  setQueriesData<TQueryFnData>(\n    filters: QueryFilters,\n    updater: Updater<TQueryFnData | undefined, TQueryFnData | undefined>,\n    options?: SetDataOptions,\n  ): [QueryKey, TQueryFnData | undefined][]\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  setQueriesData<TQueryFnData>(\n    queryKey: QueryKey,\n    updater: Updater<TQueryFnData | undefined, TQueryFnData | undefined>,\n    options?: SetDataOptions,\n  ): [QueryKey, TQueryFnData | undefined][]\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  setQueriesData<TQueryFnData>(\n    queryKeyOrFilters: QueryKey | QueryFilters,\n    updater: Updater<TQueryFnData | undefined, TQueryFnData | undefined>,\n    options?: SetDataOptions,\n  ): [QueryKey, TQueryFnData | undefined][] {\n    return notifyManager.batch(() =>\n      this.getQueryCache()\n        .findAll(queryKeyOrFilters)\n        .map(({ queryKey }) => [\n          queryKey,\n          this.setQueryData<TQueryFnData>(queryKey, updater, options),\n        ]),\n    )\n  }\n\n  getQueryState<TQueryFnData = unknown, TError = undefined>(\n    queryKey: QueryKey,\n    /**\n     * @deprecated This filters will be removed in the next major version.\n     */\n    filters?: OmitKeyof<QueryFilters, 'queryKey'>,\n  ): QueryState<TQueryFnData, TError> | undefined {\n    return this.queryCache.find<TQueryFnData, TError>(queryKey, filters)?.state\n  }\n\n  removeQueries(filters?: QueryFilters): void\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  removeQueries(\n    queryKey?: QueryKey,\n    filters?: OmitKeyof<QueryFilters, 'queryKey'>,\n  ): void\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  removeQueries(\n    arg1?: QueryKey | QueryFilters,\n    arg2?: OmitKeyof<QueryFilters, 'queryKey'>,\n  ): void {\n    const [filters] = parseFilterArgs(arg1, arg2)\n    const queryCache = this.queryCache\n    notifyManager.batch(() => {\n      queryCache.findAll(filters).forEach((query) => {\n        queryCache.remove(query)\n      })\n    })\n  }\n\n  resetQueries<TPageData = unknown>(\n    filters?: ResetQueryFilters<TPageData>,\n    options?: ResetOptions,\n  ): Promise<void>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  resetQueries<TPageData = unknown>(\n    queryKey?: QueryKey,\n    filters?: OmitKeyof<ResetQueryFilters<TPageData>, 'queryKey'>,\n    options?: ResetOptions,\n  ): Promise<void>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  resetQueries(\n    arg1?: QueryKey | ResetQueryFilters,\n    arg2?: OmitKeyof<ResetQueryFilters, 'queryKey'> | ResetOptions,\n    arg3?: ResetOptions,\n  ): Promise<void> {\n    const [filters, options] = parseFilterArgs(arg1, arg2, arg3)\n    const queryCache = this.queryCache\n\n    const refetchFilters: RefetchQueryFilters = {\n      type: 'active',\n      ...filters,\n    }\n\n    return notifyManager.batch(() => {\n      queryCache.findAll(filters).forEach((query) => {\n        query.reset()\n      })\n      return this.refetchQueries(refetchFilters, options)\n    })\n  }\n\n  cancelQueries(filters?: QueryFilters, options?: CancelOptions): Promise<void>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  cancelQueries(\n    queryKey?: QueryKey,\n    filters?: OmitKeyof<QueryFilters, 'queryKey'>,\n    options?: CancelOptions,\n  ): Promise<void>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  cancelQueries(\n    arg1?: QueryKey | QueryFilters,\n    arg2?: OmitKeyof<QueryFilters, 'queryKey'> | CancelOptions,\n    arg3?: CancelOptions,\n  ): Promise<void> {\n    const [filters, cancelOptions = {}] = parseFilterArgs(arg1, arg2, arg3)\n\n    if (typeof cancelOptions.revert === 'undefined') {\n      cancelOptions.revert = true\n    }\n\n    const promises = notifyManager.batch(() =>\n      this.queryCache\n        .findAll(filters)\n        .map((query) => query.cancel(cancelOptions)),\n    )\n\n    return Promise.all(promises).then(noop).catch(noop)\n  }\n\n  invalidateQueries<TPageData = unknown>(\n    filters?: InvalidateQueryFilters<TPageData>,\n    options?: InvalidateOptions,\n  ): Promise<void>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  invalidateQueries<TPageData = unknown>(\n    queryKey?: QueryKey,\n    filters?: OmitKeyof<InvalidateQueryFilters<TPageData>, 'queryKey'>,\n    options?: InvalidateOptions,\n  ): Promise<void>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  invalidateQueries(\n    arg1?: QueryKey | InvalidateQueryFilters,\n    arg2?: OmitKeyof<InvalidateQueryFilters, 'queryKey'> | InvalidateOptions,\n    arg3?: InvalidateOptions,\n  ): Promise<void> {\n    const [filters, options] = parseFilterArgs(arg1, arg2, arg3)\n\n    return notifyManager.batch(() => {\n      this.queryCache.findAll(filters).forEach((query) => {\n        query.invalidate()\n      })\n\n      if (filters.refetchType === 'none') {\n        return Promise.resolve()\n      }\n      const refetchFilters: RefetchQueryFilters = {\n        ...filters,\n        type: filters.refetchType ?? filters.type ?? 'active',\n      }\n      return this.refetchQueries(refetchFilters, options)\n    })\n  }\n\n  refetchQueries<TPageData = unknown>(\n    filters?: RefetchQueryFilters<TPageData>,\n    options?: RefetchOptions,\n  ): Promise<void>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  refetchQueries<TPageData = unknown>(\n    queryKey?: QueryKey,\n    filters?: OmitKeyof<RefetchQueryFilters<TPageData>, 'queryKey'>,\n    options?: RefetchOptions,\n  ): Promise<void>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  refetchQueries(\n    arg1?: QueryKey | RefetchQueryFilters,\n    arg2?: OmitKeyof<RefetchQueryFilters, 'queryKey'> | RefetchOptions,\n    arg3?: RefetchOptions,\n  ): Promise<void> {\n    const [filters, options] = parseFilterArgs(arg1, arg2, arg3)\n\n    const promises = notifyManager.batch(() =>\n      this.queryCache\n        .findAll(filters)\n        .filter((query) => !query.isDisabled())\n        .map((query) =>\n          query.fetch(undefined, {\n            ...options,\n            cancelRefetch: options?.cancelRefetch ?? true,\n            meta: { refetchPage: filters.refetchPage },\n          }),\n        ),\n    )\n\n    let promise = Promise.all(promises).then(noop)\n\n    if (!options?.throwOnError) {\n      promise = promise.catch(noop)\n    }\n\n    return promise\n  }\n\n  fetchQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    options: FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n  ): Promise<TData>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  fetchQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    queryKey: TQueryKey,\n    options?: OmitKeyof<\n      FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n      'queryKey'\n    >,\n  ): Promise<TData>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  fetchQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    queryKey: TQueryKey,\n    queryFn: QueryFunction<TQueryFnData, TQueryKey>,\n    options?: OmitKeyof<\n      FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n      'queryKey' | 'queryFn'\n    >,\n  ): Promise<TData>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  fetchQuery<\n    TQueryFnData,\n    TError,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    arg1: TQueryKey | FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    arg2?:\n      | QueryFunction<TQueryFnData, TQueryKey>\n      | OmitKeyof<\n          FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n          'queryKey'\n        >,\n    arg3?: OmitKeyof<\n      FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n      'queryKey' | 'queryFn'\n    >,\n  ): Promise<TData> {\n    const parsedOptions = parseQueryArgs(arg1, arg2, arg3)\n    const defaultedOptions = this.defaultQueryOptions(parsedOptions)\n\n    // https://github.com/tannerlinsley/react-query/issues/652\n    if (typeof defaultedOptions.retry === 'undefined') {\n      defaultedOptions.retry = false\n    }\n\n    const query = this.queryCache.build(this, defaultedOptions)\n\n    return query.isStaleByTime(defaultedOptions.staleTime)\n      ? query.fetch(defaultedOptions)\n      : Promise.resolve(query.state.data as TData)\n  }\n\n  prefetchQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    options: FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n  ): Promise<void>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  prefetchQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    queryKey: TQueryKey,\n    options?: OmitKeyof<\n      FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n      'queryKey'\n    >,\n  ): Promise<void>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  prefetchQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    queryKey: TQueryKey,\n    queryFn: QueryFunction<TQueryFnData, TQueryKey>,\n    options?: OmitKeyof<\n      FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n      'queryKey' | 'queryFn'\n    >,\n  ): Promise<void>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  prefetchQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    arg1: TQueryKey | FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    arg2?:\n      | QueryFunction<TQueryFnData, TQueryKey>\n      | OmitKeyof<\n          FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n          'queryKey'\n        >,\n    arg3?: OmitKeyof<\n      FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n      'queryKey' | 'queryFn'\n    >,\n  ): Promise<void> {\n    return this.fetchQuery(arg1 as any, arg2 as any, arg3)\n      .then(noop)\n      .catch(noop)\n  }\n\n  fetchInfiniteQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    options: FetchInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n  ): Promise<InfiniteData<TData>>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  fetchInfiniteQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    queryKey: TQueryKey,\n    options?: OmitKeyof<\n      FetchInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n      'queryKey'\n    >,\n  ): Promise<InfiniteData<TData>>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  fetchInfiniteQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    queryKey: TQueryKey,\n    queryFn: QueryFunction<TQueryFnData, TQueryKey>,\n    options?: OmitKeyof<\n      FetchInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n      'queryKey' | 'queryFn'\n    >,\n  ): Promise<InfiniteData<TData>>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  fetchInfiniteQuery<\n    TQueryFnData,\n    TError,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    arg1:\n      | TQueryKey\n      | FetchInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    arg2?:\n      | QueryFunction<TQueryFnData, TQueryKey>\n      | OmitKeyof<\n          FetchInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n          'queryKey'\n        >,\n    arg3?: OmitKeyof<\n      FetchInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n      'queryKey' | 'queryFn'\n    >,\n  ): Promise<InfiniteData<TData>> {\n    const parsedOptions = parseQueryArgs(arg1, arg2, arg3)\n    parsedOptions.behavior = infiniteQueryBehavior<\n      TQueryFnData,\n      TError,\n      TData\n    >()\n    return this.fetchQuery(parsedOptions)\n  }\n\n  prefetchInfiniteQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    options: FetchInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n  ): Promise<void>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  prefetchInfiniteQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    queryKey: TQueryKey,\n    options?: OmitKeyof<\n      FetchInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n      'queryKey'\n    >,\n  ): Promise<void>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  prefetchInfiniteQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    queryKey: TQueryKey,\n    queryFn: QueryFunction<TQueryFnData, TQueryKey>,\n    options?: OmitKeyof<\n      FetchInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n      'queryKey' | 'queryFn'\n    >,\n  ): Promise<void>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  prefetchInfiniteQuery<\n    TQueryFnData,\n    TError,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    arg1:\n      | TQueryKey\n      | FetchInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    arg2?:\n      | QueryFunction<TQueryFnData, TQueryKey>\n      | OmitKeyof<\n          FetchInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n          'queryKey'\n        >,\n    arg3?: OmitKeyof<\n      FetchInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n      'queryKey' | 'queryFn'\n    >,\n  ): Promise<void> {\n    return this.fetchInfiniteQuery(arg1 as any, arg2 as any, arg3)\n      .then(noop)\n      .catch(noop)\n  }\n\n  resumePausedMutations(): Promise<unknown> {\n    return this.mutationCache.resumePausedMutations()\n  }\n\n  getQueryCache(): QueryCache {\n    return this.queryCache\n  }\n\n  getMutationCache(): MutationCache {\n    return this.mutationCache\n  }\n\n  getLogger(): Logger {\n    return this.logger\n  }\n\n  getDefaultOptions(): DefaultOptions {\n    return this.defaultOptions\n  }\n\n  setDefaultOptions(options: DefaultOptions): void {\n    this.defaultOptions = options\n  }\n\n  setQueryDefaults(\n    queryKey: QueryKey,\n    options: QueryObserverOptions<unknown, any, any, any>,\n  ): void {\n    const result = this.queryDefaults.find(\n      (x) => hashQueryKey(queryKey) === hashQueryKey(x.queryKey),\n    )\n    if (result) {\n      result.defaultOptions = options\n    } else {\n      this.queryDefaults.push({ queryKey, defaultOptions: options })\n    }\n  }\n\n  getQueryDefaults(\n    queryKey?: QueryKey,\n  ): QueryObserverOptions<any, any, any, any, any> | undefined {\n    if (!queryKey) {\n      return undefined\n    }\n\n    // Get the first matching defaults\n    const firstMatchingDefaults = this.queryDefaults.find((x) =>\n      partialMatchKey(queryKey, x.queryKey),\n    )\n\n    // Additional checks and error in dev mode\n    if (process.env.NODE_ENV !== 'production') {\n      // Retrieve all matching defaults for the given key\n      const matchingDefaults = this.queryDefaults.filter((x) =>\n        partialMatchKey(queryKey, x.queryKey),\n      )\n      // It is ok not having defaults, but it is error prone to have more than 1 default for a given key\n      if (matchingDefaults.length > 1) {\n        this.logger.error(\n          `[QueryClient] Several query defaults match with key '${JSON.stringify(\n            queryKey,\n          )}'. The first matching query defaults are used. Please check how query defaults are registered. Order does matter here. cf. https://react-query.tanstack.com/reference/QueryClient#queryclientsetquerydefaults.`,\n        )\n      }\n    }\n\n    return firstMatchingDefaults?.defaultOptions\n  }\n\n  setMutationDefaults(\n    mutationKey: MutationKey,\n    options: MutationObserverOptions<any, any, any, any>,\n  ): void {\n    const result = this.mutationDefaults.find(\n      (x) => hashQueryKey(mutationKey) === hashQueryKey(x.mutationKey),\n    )\n    if (result) {\n      result.defaultOptions = options\n    } else {\n      this.mutationDefaults.push({ mutationKey, defaultOptions: options })\n    }\n  }\n\n  getMutationDefaults(\n    mutationKey?: MutationKey,\n  ): MutationObserverOptions<any, any, any, any> | undefined {\n    if (!mutationKey) {\n      return undefined\n    }\n\n    // Get the first matching defaults\n    const firstMatchingDefaults = this.mutationDefaults.find((x) =>\n      partialMatchKey(mutationKey, x.mutationKey),\n    )\n\n    // Additional checks and error in dev mode\n    if (process.env.NODE_ENV !== 'production') {\n      // Retrieve all matching defaults for the given key\n      const matchingDefaults = this.mutationDefaults.filter((x) =>\n        partialMatchKey(mutationKey, x.mutationKey),\n      )\n      // It is ok not having defaults, but it is error prone to have more than 1 default for a given key\n      if (matchingDefaults.length > 1) {\n        this.logger.error(\n          `[QueryClient] Several mutation defaults match with key '${JSON.stringify(\n            mutationKey,\n          )}'. The first matching mutation defaults are used. Please check how mutation defaults are registered. Order does matter here. cf. https://react-query.tanstack.com/reference/QueryClient#queryclientsetmutationdefaults.`,\n        )\n      }\n    }\n\n    return firstMatchingDefaults?.defaultOptions\n  }\n\n  defaultQueryOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryData,\n    TQueryKey extends QueryKey,\n  >(\n    options?:\n      | QueryObserverOptions<TQueryFnData, TError, TData, TQueryData, TQueryKey>\n      | DefaultedQueryObserverOptions<\n          TQueryFnData,\n          TError,\n          TData,\n          TQueryData,\n          TQueryKey\n        >,\n  ): DefaultedQueryObserverOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryData,\n    TQueryKey\n  > {\n    if (options?._defaulted) {\n      return options as DefaultedQueryObserverOptions<\n        TQueryFnData,\n        TError,\n        TData,\n        TQueryData,\n        TQueryKey\n      >\n    }\n\n    const defaultedOptions = {\n      ...this.defaultOptions.queries,\n      ...this.getQueryDefaults(options?.queryKey),\n      ...options,\n      _defaulted: true,\n    }\n\n    if (!defaultedOptions.queryHash && defaultedOptions.queryKey) {\n      defaultedOptions.queryHash = hashQueryKeyByOptions(\n        defaultedOptions.queryKey,\n        defaultedOptions,\n      )\n    }\n\n    // dependent default values\n    if (typeof defaultedOptions.refetchOnReconnect === 'undefined') {\n      defaultedOptions.refetchOnReconnect =\n        defaultedOptions.networkMode !== 'always'\n    }\n    if (typeof defaultedOptions.useErrorBoundary === 'undefined') {\n      defaultedOptions.useErrorBoundary = !!defaultedOptions.suspense\n    }\n\n    return defaultedOptions as DefaultedQueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey\n    >\n  }\n\n  defaultMutationOptions<T extends MutationOptions<any, any, any, any>>(\n    options?: T,\n  ): T {\n    if (options?._defaulted) {\n      return options\n    }\n    return {\n      ...this.defaultOptions.mutations,\n      ...this.getMutationDefaults(options?.mutationKey),\n      ...options,\n      _defaulted: true,\n    } as T\n  }\n\n  clear(): void {\n    this.queryCache.clear()\n    this.mutationCache.clear()\n  }\n}\n"], "names": ["QueryClient", "constructor", "config", "queryCache", "Query<PERSON>ache", "mutationCache", "MutationCache", "logger", "defaultLogger", "defaultOptions", "queryDefaults", "mutationDefaults", "mountCount", "process", "env", "NODE_ENV", "error", "mount", "unsubscribeFocus", "focusManager", "subscribe", "isFocused", "resumePausedMutations", "onFocus", "unsubscribeOnline", "onlineManager", "isOnline", "onOnline", "unmount", "undefined", "isFetching", "arg1", "arg2", "filters", "parseFilter<PERSON><PERSON>s", "fetchStatus", "findAll", "length", "isMutating", "fetching", "getQueryData", "query<PERSON><PERSON>", "find", "state", "data", "ensureQueryData", "arg3", "parsedOptions", "parse<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cachedData", "Promise", "resolve", "<PERSON><PERSON><PERSON><PERSON>", "getQueriesData", "query<PERSON>eyOrFilters", "get<PERSON><PERSON><PERSON><PERSON>ache", "map", "setQueryData", "updater", "options", "query", "prevData", "functionalUpdate", "defaultedOptions", "defaultQueryOptions", "build", "setData", "manual", "setQueriesData", "notify<PERSON><PERSON>ger", "batch", "getQueryState", "removeQueries", "for<PERSON>ach", "remove", "resetQueries", "refetchFilters", "type", "reset", "refetchQueries", "cancelQueries", "cancelOptions", "revert", "promises", "cancel", "all", "then", "noop", "catch", "invalidateQueries", "invalidate", "refetchType", "filter", "isDisabled", "fetch", "cancelRefetch", "meta", "refetchPage", "promise", "throwOnError", "retry", "isStaleByTime", "staleTime", "prefetch<PERSON><PERSON>y", "fetchInfiniteQuery", "behavior", "infiniteQueryBehavior", "prefetchInfiniteQuery", "getMutationCache", "<PERSON><PERSON><PERSON><PERSON>", "getDefaultOptions", "setDefaultOptions", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "result", "x", "hashQuery<PERSON>ey", "push", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "firstMatchingDefaults", "partialMatchKey", "matchingDefaults", "JSON", "stringify", "setMutationDefaults", "<PERSON><PERSON><PERSON>", "getMutationDefaults", "_defaulted", "queries", "queryHash", "hashQueryKeyByOptions", "refetchOnReconnect", "networkMode", "useErrorBoundary", "suspense", "defaultMutationOptions", "mutations", "clear"], "mappings": ";;;;;;;;;AAwDA;AAEO,MAAMA,WAAN,CAAkB;AAWvBC,EAAAA,WAAW,CAACC,MAAyB,GAAG,EAA7B,EAAiC;IAC1C,IAAKC,CAAAA,UAAL,GAAkBD,MAAM,CAACC,UAAP,IAAqB,IAAIC,UAAJ,EAAvC,CAAA;IACA,IAAKC,CAAAA,aAAL,GAAqBH,MAAM,CAACG,aAAP,IAAwB,IAAIC,aAAJ,EAA7C,CAAA;AACA,IAAA,IAAA,CAAKC,MAAL,GAAcL,MAAM,CAACK,MAAP,IAAiBC,aAA/B,CAAA;AACA,IAAA,IAAA,CAAKC,cAAL,GAAsBP,MAAM,CAACO,cAAP,IAAyB,EAA/C,CAAA;IACA,IAAKC,CAAAA,aAAL,GAAqB,EAArB,CAAA;IACA,IAAKC,CAAAA,gBAAL,GAAwB,EAAxB,CAAA;IACA,IAAKC,CAAAA,UAAL,GAAkB,CAAlB,CAAA;;IAEA,IAAIC,OAAO,CAACC,GAAR,CAAYC,QAAZ,KAAyB,YAAzB,IAAyCb,MAAM,CAACK,MAApD,EAA4D;MAC1D,IAAKA,CAAAA,MAAL,CAAYS,KAAZ,CAAA,4FAAA,CAAA,CAAA;AAGD,KAAA;AACF,GAAA;;AAEDC,EAAAA,KAAK,GAAS;AACZ,IAAA,IAAA,CAAKL,UAAL,EAAA,CAAA;AACA,IAAA,IAAI,IAAKA,CAAAA,UAAL,KAAoB,CAAxB,EAA2B,OAAA;AAE3B,IAAA,IAAA,CAAKM,gBAAL,GAAwBC,YAAY,CAACC,SAAb,CAAuB,MAAM;AACnD,MAAA,IAAID,YAAY,CAACE,SAAb,EAAJ,EAA8B;AAC5B,QAAA,IAAA,CAAKC,qBAAL,EAAA,CAAA;QACA,IAAKnB,CAAAA,UAAL,CAAgBoB,OAAhB,EAAA,CAAA;AACD,OAAA;AACF,KALuB,CAAxB,CAAA;AAMA,IAAA,IAAA,CAAKC,iBAAL,GAAyBC,aAAa,CAACL,SAAd,CAAwB,MAAM;AACrD,MAAA,IAAIK,aAAa,CAACC,QAAd,EAAJ,EAA8B;AAC5B,QAAA,IAAA,CAAKJ,qBAAL,EAAA,CAAA;QACA,IAAKnB,CAAAA,UAAL,CAAgBwB,QAAhB,EAAA,CAAA;AACD,OAAA;AACF,KALwB,CAAzB,CAAA;AAMD,GAAA;;AAEDC,EAAAA,OAAO,GAAS;AAAA,IAAA,IAAA,qBAAA,EAAA,qBAAA,CAAA;;AACd,IAAA,IAAA,CAAKhB,UAAL,EAAA,CAAA;AACA,IAAA,IAAI,IAAKA,CAAAA,UAAL,KAAoB,CAAxB,EAA2B,OAAA;AAE3B,IAAA,CAAA,qBAAA,GAAA,IAAA,CAAKM,gBAAL,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,qBAAA,CAAA,IAAA,CAAA,IAAA,CAAA,CAAA;IACA,IAAKA,CAAAA,gBAAL,GAAwBW,SAAxB,CAAA;AAEA,IAAA,CAAA,qBAAA,GAAA,IAAA,CAAKL,iBAAL,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,qBAAA,CAAA,IAAA,CAAA,IAAA,CAAA,CAAA;IACA,IAAKA,CAAAA,iBAAL,GAAyBK,SAAzB,CAAA;AACD,GAAA;;AAUD;AACF;AACA;AACEC,EAAAA,UAAU,CAACC,IAAD,EAAiCC,IAAjC,EAA8D;IACtE,MAAM,CAACC,OAAD,CAAYC,GAAAA,eAAe,CAACH,IAAD,EAAOC,IAAP,CAAjC,CAAA;IACAC,OAAO,CAACE,WAAR,GAAsB,UAAtB,CAAA;AACA,IAAA,OAAO,KAAKhC,UAAL,CAAgBiC,OAAhB,CAAwBH,OAAxB,EAAiCI,MAAxC,CAAA;AACD,GAAA;;EAEDC,UAAU,CAACL,OAAD,EAAoC;AAC5C,IAAA,OAAO,KAAK5B,aAAL,CAAmB+B,OAAnB,CAA2B,EAAE,GAAGH,OAAL;AAAcM,MAAAA,QAAQ,EAAE,IAAA;AAAxB,KAA3B,EAA2DF,MAAlE,CAAA;AACD,GAAA;;AAYD;AACF;AACA;AACEG,EAAAA,YAAY,CACVC,QADU,EAEVR,OAFU,EAGgB;AAAA,IAAA,IAAA,qBAAA,CAAA;;AAC1B,IAAA,OAAA,CAAA,qBAAA,GAAO,IAAK9B,CAAAA,UAAL,CAAgBuC,IAAhB,CAAmCD,QAAnC,EAA6CR,OAA7C,CAAP,KAAA,IAAA,GAAA,KAAA,CAAA,GAAO,qBAAuDU,CAAAA,KAAvD,CAA6DC,IAApE,CAAA;AACD,GAAA;;AA4CD;AACF;AACA;AACEC,EAAAA,eAAe,CAMbd,IANa,EAYbC,IAZa,EAebc,IAfa,EAgBG;IAChB,MAAMC,aAAa,GAAGC,cAAc,CAACjB,IAAD,EAAOC,IAAP,EAAac,IAAb,CAApC,CAAA;IACA,MAAMG,UAAU,GAAG,IAAKT,CAAAA,YAAL,CAAyBO,aAAa,CAACN,QAAvC,CAAnB,CAAA;AAEA,IAAA,OAAOQ,UAAU,GACbC,OAAO,CAACC,OAAR,CAAgBF,UAAhB,CADa,GAEb,IAAA,CAAKG,UAAL,CAAgBL,aAAhB,CAFJ,CAAA;AAGD,GAAA;;AAWD;AACF;AACA;EACEM,cAAc,CACZC,iBADY,EAE4B;IACxC,OAAO,IAAA,CAAKC,aAAL,EACJnB,CAAAA,OADI,CACIkB,iBADJ,CAAA,CAEJE,GAFI,CAEA,CAAC;MAAEf,QAAF;AAAYE,MAAAA,KAAAA;AAAZ,KAAD,KAAyB;AAC5B,MAAA,MAAMC,IAAI,GAAGD,KAAK,CAACC,IAAnB,CAAA;AACA,MAAA,OAAO,CAACH,QAAD,EAAWG,IAAX,CAAP,CAAA;AACD,KALI,CAAP,CAAA;AAMD,GAAA;;AAEDa,EAAAA,YAAY,CACVhB,QADU,EAEViB,OAFU,EAGVC,OAHU,EAIgB;IAC1B,MAAMC,KAAK,GAAG,IAAKzD,CAAAA,UAAL,CAAgBuC,IAAhB,CAAmCD,QAAnC,CAAd,CAAA;IACA,MAAMoB,QAAQ,GAAGD,KAAH,IAAA,IAAA,GAAA,KAAA,CAAA,GAAGA,KAAK,CAAEjB,KAAP,CAAaC,IAA9B,CAAA;AACA,IAAA,MAAMA,IAAI,GAAGkB,gBAAgB,CAACJ,OAAD,EAAUG,QAAV,CAA7B,CAAA;;AAEA,IAAA,IAAI,OAAOjB,IAAP,KAAgB,WAApB,EAAiC;AAC/B,MAAA,OAAOf,SAAP,CAAA;AACD,KAAA;;AAED,IAAA,MAAMkB,aAAa,GAAGC,cAAc,CAACP,QAAD,CAApC,CAAA;AACA,IAAA,MAAMsB,gBAAgB,GAAG,IAAA,CAAKC,mBAAL,CAAyBjB,aAAzB,CAAzB,CAAA;AACA,IAAA,OAAO,IAAK5C,CAAAA,UAAL,CACJ8D,KADI,CACE,IADF,EACQF,gBADR,CAAA,CAEJG,OAFI,CAEItB,IAFJ,EAEU,EAAE,GAAGe,OAAL;AAAcQ,MAAAA,MAAM,EAAE,IAAA;AAAtB,KAFV,CAAP,CAAA;AAGD,GAAA;;AAeD;AACF;AACA;AACEC,EAAAA,cAAc,CACZd,iBADY,EAEZI,OAFY,EAGZC,OAHY,EAI4B;AACxC,IAAA,OAAOU,aAAa,CAACC,KAAd,CAAoB,MACzB,IAAKf,CAAAA,aAAL,EACGnB,CAAAA,OADH,CACWkB,iBADX,CAEGE,CAAAA,GAFH,CAEO,CAAC;AAAEf,MAAAA,QAAAA;AAAF,KAAD,KAAkB,CACrBA,QADqB,EAErB,KAAKgB,YAAL,CAAgChB,QAAhC,EAA0CiB,OAA1C,EAAmDC,OAAnD,CAFqB,CAFzB,CADK,CAAP,CAAA;AAQD,GAAA;;AAEDY,EAAAA,aAAa,CACX9B,QADW;AAEX;AACJ;AACA;AACIR,EAAAA,OALW,EAMmC;AAAA,IAAA,IAAA,sBAAA,CAAA;;IAC9C,OAAO,CAAA,sBAAA,GAAA,IAAA,CAAK9B,UAAL,CAAgBuC,IAAhB,CAA2CD,QAA3C,EAAqDR,OAArD,CAAP,KAAO,IAAA,GAAA,KAAA,CAAA,GAAA,sBAAA,CAA+DU,KAAtE,CAAA;AACD,GAAA;;AAUD;AACF;AACA;AACE6B,EAAAA,aAAa,CACXzC,IADW,EAEXC,IAFW,EAGL;IACN,MAAM,CAACC,OAAD,CAAYC,GAAAA,eAAe,CAACH,IAAD,EAAOC,IAAP,CAAjC,CAAA;IACA,MAAM7B,UAAU,GAAG,IAAA,CAAKA,UAAxB,CAAA;IACAkE,aAAa,CAACC,KAAd,CAAoB,MAAM;MACxBnE,UAAU,CAACiC,OAAX,CAAmBH,OAAnB,EAA4BwC,OAA5B,CAAqCb,KAAD,IAAW;QAC7CzD,UAAU,CAACuE,MAAX,CAAkBd,KAAlB,CAAA,CAAA;OADF,CAAA,CAAA;KADF,CAAA,CAAA;AAKD,GAAA;;AAcD;AACF;AACA;AACEe,EAAAA,YAAY,CACV5C,IADU,EAEVC,IAFU,EAGVc,IAHU,EAIK;AACf,IAAA,MAAM,CAACb,OAAD,EAAU0B,OAAV,CAAqBzB,GAAAA,eAAe,CAACH,IAAD,EAAOC,IAAP,EAAac,IAAb,CAA1C,CAAA;IACA,MAAM3C,UAAU,GAAG,IAAA,CAAKA,UAAxB,CAAA;AAEA,IAAA,MAAMyE,cAAmC,GAAG;AAC1CC,MAAAA,IAAI,EAAE,QADoC;MAE1C,GAAG5C,OAAAA;KAFL,CAAA;AAKA,IAAA,OAAOoC,aAAa,CAACC,KAAd,CAAoB,MAAM;MAC/BnE,UAAU,CAACiC,OAAX,CAAmBH,OAAnB,EAA4BwC,OAA5B,CAAqCb,KAAD,IAAW;AAC7CA,QAAAA,KAAK,CAACkB,KAAN,EAAA,CAAA;OADF,CAAA,CAAA;AAGA,MAAA,OAAO,KAAKC,cAAL,CAAoBH,cAApB,EAAoCjB,OAApC,CAAP,CAAA;AACD,KALM,CAAP,CAAA;AAMD,GAAA;;AAWD;AACF;AACA;AACEqB,EAAAA,aAAa,CACXjD,IADW,EAEXC,IAFW,EAGXc,IAHW,EAII;AACf,IAAA,MAAM,CAACb,OAAD,EAAUgD,aAAa,GAAG,EAA1B,CAAA,GAAgC/C,eAAe,CAACH,IAAD,EAAOC,IAAP,EAAac,IAAb,CAArD,CAAA;;AAEA,IAAA,IAAI,OAAOmC,aAAa,CAACC,MAArB,KAAgC,WAApC,EAAiD;MAC/CD,aAAa,CAACC,MAAd,GAAuB,IAAvB,CAAA;AACD,KAAA;;IAED,MAAMC,QAAQ,GAAGd,aAAa,CAACC,KAAd,CAAoB,MACnC,IAAA,CAAKnE,UAAL,CACGiC,OADH,CACWH,OADX,CAEGuB,CAAAA,GAFH,CAEQI,KAAD,IAAWA,KAAK,CAACwB,MAAN,CAAaH,aAAb,CAFlB,CADe,CAAjB,CAAA;AAMA,IAAA,OAAO/B,OAAO,CAACmC,GAAR,CAAYF,QAAZ,CAAA,CAAsBG,IAAtB,CAA2BC,IAA3B,CAAA,CAAiCC,KAAjC,CAAuCD,IAAvC,CAAP,CAAA;AACD,GAAA;;AAcD;AACF;AACA;AACEE,EAAAA,iBAAiB,CACf1D,IADe,EAEfC,IAFe,EAGfc,IAHe,EAIA;AACf,IAAA,MAAM,CAACb,OAAD,EAAU0B,OAAV,CAAqBzB,GAAAA,eAAe,CAACH,IAAD,EAAOC,IAAP,EAAac,IAAb,CAA1C,CAAA;AAEA,IAAA,OAAOuB,aAAa,CAACC,KAAd,CAAoB,MAAM;AAAA,MAAA,IAAA,IAAA,EAAA,oBAAA,CAAA;;MAC/B,IAAKnE,CAAAA,UAAL,CAAgBiC,OAAhB,CAAwBH,OAAxB,CAAiCwC,CAAAA,OAAjC,CAA0Cb,KAAD,IAAW;AAClDA,QAAAA,KAAK,CAAC8B,UAAN,EAAA,CAAA;OADF,CAAA,CAAA;;AAIA,MAAA,IAAIzD,OAAO,CAAC0D,WAAR,KAAwB,MAA5B,EAAoC;QAClC,OAAOzC,OAAO,CAACC,OAAR,EAAP,CAAA;AACD,OAAA;;AACD,MAAA,MAAMyB,cAAmC,GAAG,EAC1C,GAAG3C,OADuC;QAE1C4C,IAAI,EAAA,CAAA,IAAA,GAAA,CAAA,oBAAA,GAAE5C,OAAO,CAAC0D,WAAV,mCAAyB1D,OAAO,CAAC4C,IAAjC,KAAyC,IAAA,GAAA,IAAA,GAAA,QAAA;OAF/C,CAAA;AAIA,MAAA,OAAO,KAAKE,cAAL,CAAoBH,cAApB,EAAoCjB,OAApC,CAAP,CAAA;AACD,KAbM,CAAP,CAAA;AAcD,GAAA;;AAcD;AACF;AACA;AACEoB,EAAAA,cAAc,CACZhD,IADY,EAEZC,IAFY,EAGZc,IAHY,EAIG;AACf,IAAA,MAAM,CAACb,OAAD,EAAU0B,OAAV,CAAqBzB,GAAAA,eAAe,CAACH,IAAD,EAAOC,IAAP,EAAac,IAAb,CAA1C,CAAA;IAEA,MAAMqC,QAAQ,GAAGd,aAAa,CAACC,KAAd,CAAoB,MACnC,IAAKnE,CAAAA,UAAL,CACGiC,OADH,CACWH,OADX,EAEG2D,MAFH,CAEWhC,KAAD,IAAW,CAACA,KAAK,CAACiC,UAAN,EAFtB,CAAA,CAGGrC,GAHH,CAGQI,KAAD,IAAA;AAAA,MAAA,IAAA,qBAAA,CAAA;;MAAA,OACHA,KAAK,CAACkC,KAAN,CAAYjE,SAAZ,EAAuB,EACrB,GAAG8B,OADkB;AAErBoC,QAAAA,aAAa,2BAAEpC,OAAF,IAAA,IAAA,GAAA,KAAA,CAAA,GAAEA,OAAO,CAAEoC,aAAX,oCAA4B,IAFpB;AAGrBC,QAAAA,IAAI,EAAE;UAAEC,WAAW,EAAEhE,OAAO,CAACgE,WAAAA;AAAvB,SAAA;AAHe,OAAvB,CADG,CAAA;AAAA,KAHP,CADe,CAAjB,CAAA;IAaA,IAAIC,OAAO,GAAGhD,OAAO,CAACmC,GAAR,CAAYF,QAAZ,CAAsBG,CAAAA,IAAtB,CAA2BC,IAA3B,CAAd,CAAA;;AAEA,IAAA,IAAI,EAAC5B,OAAD,IAAA,IAAA,IAACA,OAAO,CAAEwC,YAAV,CAAJ,EAA4B;AAC1BD,MAAAA,OAAO,GAAGA,OAAO,CAACV,KAAR,CAAcD,IAAd,CAAV,CAAA;AACD,KAAA;;AAED,IAAA,OAAOW,OAAP,CAAA;AACD,GAAA;;AAyCD;AACF;AACA;AACE9C,EAAAA,UAAU,CAMRrB,IANQ,EAORC,IAPQ,EAaRc,IAbQ,EAiBQ;IAChB,MAAMC,aAAa,GAAGC,cAAc,CAACjB,IAAD,EAAOC,IAAP,EAAac,IAAb,CAApC,CAAA;IACA,MAAMiB,gBAAgB,GAAG,IAAKC,CAAAA,mBAAL,CAAyBjB,aAAzB,CAAzB,CAFgB;;AAKhB,IAAA,IAAI,OAAOgB,gBAAgB,CAACqC,KAAxB,KAAkC,WAAtC,EAAmD;MACjDrC,gBAAgB,CAACqC,KAAjB,GAAyB,KAAzB,CAAA;AACD,KAAA;;IAED,MAAMxC,KAAK,GAAG,IAAA,CAAKzD,UAAL,CAAgB8D,KAAhB,CAAsB,IAAtB,EAA4BF,gBAA5B,CAAd,CAAA;IAEA,OAAOH,KAAK,CAACyC,aAAN,CAAoBtC,gBAAgB,CAACuC,SAArC,CAAA,GACH1C,KAAK,CAACkC,KAAN,CAAY/B,gBAAZ,CADG,GAEHb,OAAO,CAACC,OAAR,CAAgBS,KAAK,CAACjB,KAAN,CAAYC,IAA5B,CAFJ,CAAA;AAGD,GAAA;;AAyCD;AACF;AACA;AACE2D,EAAAA,aAAa,CAMXxE,IANW,EAOXC,IAPW,EAaXc,IAbW,EAiBI;AACf,IAAA,OAAO,KAAKM,UAAL,CAAgBrB,IAAhB,EAA6BC,IAA7B,EAA0Cc,IAA1C,CACJwC,CAAAA,IADI,CACCC,IADD,CAAA,CAEJC,KAFI,CAEED,IAFF,CAAP,CAAA;AAGD,GAAA;;AAyCD;AACF;AACA;AACEiB,EAAAA,kBAAkB,CAMhBzE,IANgB,EAShBC,IATgB,EAehBc,IAfgB,EAmBc;IAC9B,MAAMC,aAAa,GAAGC,cAAc,CAACjB,IAAD,EAAOC,IAAP,EAAac,IAAb,CAApC,CAAA;AACAC,IAAAA,aAAa,CAAC0D,QAAd,GAAyBC,qBAAqB,EAA9C,CAAA;AAKA,IAAA,OAAO,IAAKtD,CAAAA,UAAL,CAAgBL,aAAhB,CAAP,CAAA;AACD,GAAA;;AAyCD;AACF;AACA;AACE4D,EAAAA,qBAAqB,CAMnB5E,IANmB,EASnBC,IATmB,EAenBc,IAfmB,EAmBJ;AACf,IAAA,OAAO,KAAK0D,kBAAL,CAAwBzE,IAAxB,EAAqCC,IAArC,EAAkDc,IAAlD,CACJwC,CAAAA,IADI,CACCC,IADD,CAAA,CAEJC,KAFI,CAEED,IAFF,CAAP,CAAA;AAGD,GAAA;;AAEDjE,EAAAA,qBAAqB,GAAqB;AACxC,IAAA,OAAO,IAAKjB,CAAAA,aAAL,CAAmBiB,qBAAnB,EAAP,CAAA;AACD,GAAA;;AAEDiC,EAAAA,aAAa,GAAe;AAC1B,IAAA,OAAO,KAAKpD,UAAZ,CAAA;AACD,GAAA;;AAEDyG,EAAAA,gBAAgB,GAAkB;AAChC,IAAA,OAAO,KAAKvG,aAAZ,CAAA;AACD,GAAA;;AAEDwG,EAAAA,SAAS,GAAW;AAClB,IAAA,OAAO,KAAKtG,MAAZ,CAAA;AACD,GAAA;;AAEDuG,EAAAA,iBAAiB,GAAmB;AAClC,IAAA,OAAO,KAAKrG,cAAZ,CAAA;AACD,GAAA;;EAEDsG,iBAAiB,CAACpD,OAAD,EAAgC;IAC/C,IAAKlD,CAAAA,cAAL,GAAsBkD,OAAtB,CAAA;AACD,GAAA;;AAEDqD,EAAAA,gBAAgB,CACdvE,QADc,EAEdkB,OAFc,EAGR;IACN,MAAMsD,MAAM,GAAG,IAAKvG,CAAAA,aAAL,CAAmBgC,IAAnB,CACZwE,CAAD,IAAOC,YAAY,CAAC1E,QAAD,CAAZ,KAA2B0E,YAAY,CAACD,CAAC,CAACzE,QAAH,CADjC,CAAf,CAAA;;AAGA,IAAA,IAAIwE,MAAJ,EAAY;MACVA,MAAM,CAACxG,cAAP,GAAwBkD,OAAxB,CAAA;AACD,KAFD,MAEO;MACL,IAAKjD,CAAAA,aAAL,CAAmB0G,IAAnB,CAAwB;QAAE3E,QAAF;AAAYhC,QAAAA,cAAc,EAAEkD,OAAAA;OAApD,CAAA,CAAA;AACD,KAAA;AACF,GAAA;;EAED0D,gBAAgB,CACd5E,QADc,EAE6C;IAC3D,IAAI,CAACA,QAAL,EAAe;AACb,MAAA,OAAOZ,SAAP,CAAA;AACD,KAH0D;;;AAM3D,IAAA,MAAMyF,qBAAqB,GAAG,IAAA,CAAK5G,aAAL,CAAmBgC,IAAnB,CAAyBwE,CAAD,IACpDK,eAAe,CAAC9E,QAAD,EAAWyE,CAAC,CAACzE,QAAb,CADa,CAA9B,CAN2D;;AAW3D,IAAA,IAAI5B,OAAO,CAACC,GAAR,CAAYC,QAAZ,KAAyB,YAA7B,EAA2C;AACzC;AACA,MAAA,MAAMyG,gBAAgB,GAAG,IAAA,CAAK9G,aAAL,CAAmBkF,MAAnB,CAA2BsB,CAAD,IACjDK,eAAe,CAAC9E,QAAD,EAAWyE,CAAC,CAACzE,QAAb,CADQ,CAAzB,CAFyC;;AAMzC,MAAA,IAAI+E,gBAAgB,CAACnF,MAAjB,GAA0B,CAA9B,EAAiC;QAC/B,IAAK9B,CAAAA,MAAL,CAAYS,KAAZ,CAAA,uDAAA,GAC0DyG,IAAI,CAACC,SAAL,CACtDjF,QADsD,CAD1D,GAAA,gNAAA,CAAA,CAAA;AAKD,OAAA;AACF,KAAA;;AAED,IAAA,OAAO6E,qBAAP,IAAA,IAAA,GAAA,KAAA,CAAA,GAAOA,qBAAqB,CAAE7G,cAA9B,CAAA;AACD,GAAA;;AAEDkH,EAAAA,mBAAmB,CACjBC,WADiB,EAEjBjE,OAFiB,EAGX;IACN,MAAMsD,MAAM,GAAG,IAAKtG,CAAAA,gBAAL,CAAsB+B,IAAtB,CACZwE,CAAD,IAAOC,YAAY,CAACS,WAAD,CAAZ,KAA8BT,YAAY,CAACD,CAAC,CAACU,WAAH,CADpC,CAAf,CAAA;;AAGA,IAAA,IAAIX,MAAJ,EAAY;MACVA,MAAM,CAACxG,cAAP,GAAwBkD,OAAxB,CAAA;AACD,KAFD,MAEO;MACL,IAAKhD,CAAAA,gBAAL,CAAsByG,IAAtB,CAA2B;QAAEQ,WAAF;AAAenH,QAAAA,cAAc,EAAEkD,OAAAA;OAA1D,CAAA,CAAA;AACD,KAAA;AACF,GAAA;;EAEDkE,mBAAmB,CACjBD,WADiB,EAEwC;IACzD,IAAI,CAACA,WAAL,EAAkB;AAChB,MAAA,OAAO/F,SAAP,CAAA;AACD,KAHwD;;;AAMzD,IAAA,MAAMyF,qBAAqB,GAAG,IAAA,CAAK3G,gBAAL,CAAsB+B,IAAtB,CAA4BwE,CAAD,IACvDK,eAAe,CAACK,WAAD,EAAcV,CAAC,CAACU,WAAhB,CADa,CAA9B,CANyD;;AAWzD,IAAA,IAAI/G,OAAO,CAACC,GAAR,CAAYC,QAAZ,KAAyB,YAA7B,EAA2C;AACzC;AACA,MAAA,MAAMyG,gBAAgB,GAAG,IAAA,CAAK7G,gBAAL,CAAsBiF,MAAtB,CAA8BsB,CAAD,IACpDK,eAAe,CAACK,WAAD,EAAcV,CAAC,CAACU,WAAhB,CADQ,CAAzB,CAFyC;;AAMzC,MAAA,IAAIJ,gBAAgB,CAACnF,MAAjB,GAA0B,CAA9B,EAAiC;QAC/B,IAAK9B,CAAAA,MAAL,CAAYS,KAAZ,CAAA,0DAAA,GAC6DyG,IAAI,CAACC,SAAL,CACzDE,WADyD,CAD7D,GAAA,yNAAA,CAAA,CAAA;AAKD,OAAA;AACF,KAAA;;AAED,IAAA,OAAON,qBAAP,IAAA,IAAA,GAAA,KAAA,CAAA,GAAOA,qBAAqB,CAAE7G,cAA9B,CAAA;AACD,GAAA;;EAEDuD,mBAAmB,CAOjBL,OAPiB,EAsBjB;AACA,IAAA,IAAIA,OAAJ,IAAA,IAAA,IAAIA,OAAO,CAAEmE,UAAb,EAAyB;AACvB,MAAA,OAAOnE,OAAP,CAAA;AAOD,KAAA;;AAED,IAAA,MAAMI,gBAAgB,GAAG,EACvB,GAAG,IAAKtD,CAAAA,cAAL,CAAoBsH,OADA;MAEvB,GAAG,IAAA,CAAKV,gBAAL,CAAsB1D,OAAtB,oBAAsBA,OAAO,CAAElB,QAA/B,CAFoB;AAGvB,MAAA,GAAGkB,OAHoB;AAIvBmE,MAAAA,UAAU,EAAE,IAAA;KAJd,CAAA;;IAOA,IAAI,CAAC/D,gBAAgB,CAACiE,SAAlB,IAA+BjE,gBAAgB,CAACtB,QAApD,EAA8D;MAC5DsB,gBAAgB,CAACiE,SAAjB,GAA6BC,qBAAqB,CAChDlE,gBAAgB,CAACtB,QAD+B,EAEhDsB,gBAFgD,CAAlD,CAAA;AAID,KAvBD;;;AA0BA,IAAA,IAAI,OAAOA,gBAAgB,CAACmE,kBAAxB,KAA+C,WAAnD,EAAgE;AAC9DnE,MAAAA,gBAAgB,CAACmE,kBAAjB,GACEnE,gBAAgB,CAACoE,WAAjB,KAAiC,QADnC,CAAA;AAED,KAAA;;AACD,IAAA,IAAI,OAAOpE,gBAAgB,CAACqE,gBAAxB,KAA6C,WAAjD,EAA8D;AAC5DrE,MAAAA,gBAAgB,CAACqE,gBAAjB,GAAoC,CAAC,CAACrE,gBAAgB,CAACsE,QAAvD,CAAA;AACD,KAAA;;AAED,IAAA,OAAOtE,gBAAP,CAAA;AAOD,GAAA;;EAEDuE,sBAAsB,CACpB3E,OADoB,EAEjB;AACH,IAAA,IAAIA,OAAJ,IAAA,IAAA,IAAIA,OAAO,CAAEmE,UAAb,EAAyB;AACvB,MAAA,OAAOnE,OAAP,CAAA;AACD,KAAA;;AACD,IAAA,OAAO,EACL,GAAG,IAAKlD,CAAAA,cAAL,CAAoB8H,SADlB;MAEL,GAAG,IAAA,CAAKV,mBAAL,CAAyBlE,OAAzB,oBAAyBA,OAAO,CAAEiE,WAAlC,CAFE;AAGL,MAAA,GAAGjE,OAHE;AAILmE,MAAAA,UAAU,EAAE,IAAA;KAJd,CAAA;AAMD,GAAA;;AAEDU,EAAAA,KAAK,GAAS;IACZ,IAAKrI,CAAAA,UAAL,CAAgBqI,KAAhB,EAAA,CAAA;IACA,IAAKnI,CAAAA,aAAL,CAAmBmI,KAAnB,EAAA,CAAA;AACD,GAAA;;AAn4BsB;;;;"}