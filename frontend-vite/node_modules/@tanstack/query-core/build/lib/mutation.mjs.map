{"version": 3, "file": "mutation.mjs", "sources": ["../../src/mutation.ts"], "sourcesContent": ["import { defaultLogger } from './logger'\nimport { notify<PERSON><PERSON><PERSON> } from './notifyManager'\nimport { Removable } from './removable'\nimport { canFetch, createRetry<PERSON> } from './retryer'\nimport type { MutationMeta, MutationOptions, MutationStatus } from './types'\nimport type { MutationCache } from './mutationCache'\nimport type { MutationObserver } from './mutationObserver'\nimport type { Logger } from './logger'\nimport type { <PERSON><PERSON><PERSON> } from './retryer'\n\n// TYPES\n\ninterface MutationConfig<TData, TError, TVariables, TContext> {\n  mutationId: number\n  mutationCache: MutationCache\n  options: MutationOptions<TData, TError, TVariables, TContext>\n  logger?: Logger\n  defaultOptions?: MutationOptions<TData, TError, TVariables, TContext>\n  state?: MutationState<TData, TError, TVariables, TContext>\n  meta?: MutationMeta\n}\n\nexport interface MutationState<\n  TData = unknown,\n  TError = unknown,\n  TVariables = void,\n  TContext = unknown,\n> {\n  context: TContext | undefined\n  data: TData | undefined\n  error: TError | null\n  failureCount: number\n  failureReason: TError | null\n  isPaused: boolean\n  status: MutationStatus\n  variables: TVariables | undefined\n}\n\ninterface FailedAction<TError> {\n  type: 'failed'\n  failureCount: number\n  error: TError | null\n}\n\ninterface LoadingAction<TVariables, TContext> {\n  type: 'loading'\n  variables?: TVariables\n  context?: TContext\n}\n\ninterface SuccessAction<TData> {\n  type: 'success'\n  data: TData\n}\n\ninterface ErrorAction<TError> {\n  type: 'error'\n  error: TError\n}\n\ninterface PauseAction {\n  type: 'pause'\n}\n\ninterface ContinueAction {\n  type: 'continue'\n}\n\ninterface SetStateAction<TData, TError, TVariables, TContext> {\n  type: 'setState'\n  state: MutationState<TData, TError, TVariables, TContext>\n}\n\nexport type Action<TData, TError, TVariables, TContext> =\n  | ContinueAction\n  | ErrorAction<TError>\n  | FailedAction<TError>\n  | LoadingAction<TVariables, TContext>\n  | PauseAction\n  | SetStateAction<TData, TError, TVariables, TContext>\n  | SuccessAction<TData>\n\n// CLASS\n\nexport class Mutation<\n  TData = unknown,\n  TError = unknown,\n  TVariables = void,\n  TContext = unknown,\n> extends Removable {\n  state: MutationState<TData, TError, TVariables, TContext>\n  options!: MutationOptions<TData, TError, TVariables, TContext>\n  mutationId: number\n\n  private observers: MutationObserver<TData, TError, TVariables, TContext>[]\n  private defaultOptions?: MutationOptions<TData, TError, TVariables, TContext>\n  private mutationCache: MutationCache\n  private logger: Logger\n  private retryer?: Retryer<TData>\n\n  constructor(config: MutationConfig<TData, TError, TVariables, TContext>) {\n    super()\n\n    this.defaultOptions = config.defaultOptions\n    this.mutationId = config.mutationId\n    this.mutationCache = config.mutationCache\n    this.logger = config.logger || defaultLogger\n    this.observers = []\n    this.state = config.state || getDefaultState()\n\n    this.setOptions(config.options)\n    this.scheduleGc()\n  }\n\n  setOptions(\n    options?: MutationOptions<TData, TError, TVariables, TContext>,\n  ): void {\n    this.options = { ...this.defaultOptions, ...options }\n\n    this.updateCacheTime(this.options.cacheTime)\n  }\n\n  get meta(): MutationMeta | undefined {\n    return this.options.meta\n  }\n\n  setState(state: MutationState<TData, TError, TVariables, TContext>): void {\n    this.dispatch({ type: 'setState', state })\n  }\n\n  addObserver(observer: MutationObserver<any, any, any, any>): void {\n    if (!this.observers.includes(observer)) {\n      this.observers.push(observer)\n\n      // Stop the mutation from being garbage collected\n      this.clearGcTimeout()\n\n      this.mutationCache.notify({\n        type: 'observerAdded',\n        mutation: this,\n        observer,\n      })\n    }\n  }\n\n  removeObserver(observer: MutationObserver<any, any, any, any>): void {\n    this.observers = this.observers.filter((x) => x !== observer)\n\n    this.scheduleGc()\n\n    this.mutationCache.notify({\n      type: 'observerRemoved',\n      mutation: this,\n      observer,\n    })\n  }\n\n  protected optionalRemove() {\n    if (!this.observers.length) {\n      if (this.state.status === 'loading') {\n        this.scheduleGc()\n      } else {\n        this.mutationCache.remove(this)\n      }\n    }\n  }\n\n  continue(): Promise<unknown> {\n    return this.retryer?.continue() ?? this.execute()\n  }\n\n  async execute(): Promise<TData> {\n    const executeMutation = () => {\n      this.retryer = createRetryer({\n        fn: () => {\n          if (!this.options.mutationFn) {\n            return Promise.reject('No mutationFn found')\n          }\n          return this.options.mutationFn(this.state.variables!)\n        },\n        onFail: (failureCount, error) => {\n          this.dispatch({ type: 'failed', failureCount, error })\n        },\n        onPause: () => {\n          this.dispatch({ type: 'pause' })\n        },\n        onContinue: () => {\n          this.dispatch({ type: 'continue' })\n        },\n        retry: this.options.retry ?? 0,\n        retryDelay: this.options.retryDelay,\n        networkMode: this.options.networkMode,\n      })\n\n      return this.retryer.promise\n    }\n\n    const restored = this.state.status === 'loading'\n    try {\n      if (!restored) {\n        this.dispatch({ type: 'loading', variables: this.options.variables! })\n        // Notify cache callback\n        await this.mutationCache.config.onMutate?.(\n          this.state.variables,\n          this as Mutation<unknown, unknown, unknown, unknown>,\n        )\n        const context = await this.options.onMutate?.(this.state.variables!)\n        if (context !== this.state.context) {\n          this.dispatch({\n            type: 'loading',\n            context,\n            variables: this.state.variables,\n          })\n        }\n      }\n      const data = await executeMutation()\n\n      // Notify cache callback\n      await this.mutationCache.config.onSuccess?.(\n        data,\n        this.state.variables,\n        this.state.context,\n        this as Mutation<unknown, unknown, unknown, unknown>,\n      )\n\n      await this.options.onSuccess?.(\n        data,\n        this.state.variables!,\n        this.state.context!,\n      )\n\n      // Notify cache callback\n      await this.mutationCache.config.onSettled?.(\n        data,\n        null,\n        this.state.variables,\n        this.state.context,\n        this as Mutation<unknown, unknown, unknown, unknown>,\n      )\n\n      await this.options.onSettled?.(\n        data,\n        null,\n        this.state.variables!,\n        this.state.context,\n      )\n\n      this.dispatch({ type: 'success', data })\n      return data\n    } catch (error) {\n      try {\n        // Notify cache callback\n        await this.mutationCache.config.onError?.(\n          error,\n          this.state.variables,\n          this.state.context,\n          this as Mutation<unknown, unknown, unknown, unknown>,\n        )\n\n        if (process.env.NODE_ENV !== 'production') {\n          this.logger.error(error)\n        }\n\n        await this.options.onError?.(\n          error as TError,\n          this.state.variables!,\n          this.state.context,\n        )\n\n        // Notify cache callback\n        await this.mutationCache.config.onSettled?.(\n          undefined,\n          error,\n          this.state.variables,\n          this.state.context,\n          this as Mutation<unknown, unknown, unknown, unknown>,\n        )\n\n        await this.options.onSettled?.(\n          undefined,\n          error as TError,\n          this.state.variables!,\n          this.state.context,\n        )\n        throw error\n      } finally {\n        this.dispatch({ type: 'error', error: error as TError })\n      }\n    }\n  }\n\n  private dispatch(action: Action<TData, TError, TVariables, TContext>): void {\n    const reducer = (\n      state: MutationState<TData, TError, TVariables, TContext>,\n    ): MutationState<TData, TError, TVariables, TContext> => {\n      switch (action.type) {\n        case 'failed':\n          return {\n            ...state,\n            failureCount: action.failureCount,\n            failureReason: action.error,\n          }\n        case 'pause':\n          return {\n            ...state,\n            isPaused: true,\n          }\n        case 'continue':\n          return {\n            ...state,\n            isPaused: false,\n          }\n        case 'loading':\n          return {\n            ...state,\n            context: action.context,\n            data: undefined,\n            failureCount: 0,\n            failureReason: null,\n            error: null,\n            isPaused: !canFetch(this.options.networkMode),\n            status: 'loading',\n            variables: action.variables,\n          }\n        case 'success':\n          return {\n            ...state,\n            data: action.data,\n            failureCount: 0,\n            failureReason: null,\n            error: null,\n            status: 'success',\n            isPaused: false,\n          }\n        case 'error':\n          return {\n            ...state,\n            data: undefined,\n            error: action.error,\n            failureCount: state.failureCount + 1,\n            failureReason: action.error,\n            isPaused: false,\n            status: 'error',\n          }\n        case 'setState':\n          return {\n            ...state,\n            ...action.state,\n          }\n      }\n    }\n    this.state = reducer(this.state)\n\n    notifyManager.batch(() => {\n      this.observers.forEach((observer) => {\n        observer.onMutationUpdate(action)\n      })\n      this.mutationCache.notify({\n        mutation: this,\n        type: 'updated',\n        action,\n      })\n    })\n  }\n}\n\nexport function getDefaultState<\n  TData,\n  TError,\n  TVariables,\n  TContext,\n>(): MutationState<TData, TError, TVariables, TContext> {\n  return {\n    context: undefined,\n    data: undefined,\n    error: null,\n    failureCount: 0,\n    failureReason: null,\n    isPaused: false,\n    status: 'idle',\n    variables: undefined,\n  }\n}\n"], "names": ["Mutation", "Removable", "constructor", "config", "defaultOptions", "mutationId", "mutationCache", "logger", "defaultLogger", "observers", "state", "getDefaultState", "setOptions", "options", "scheduleGc", "updateCacheTime", "cacheTime", "meta", "setState", "dispatch", "type", "addObserver", "observer", "includes", "push", "clearGcTimeout", "notify", "mutation", "removeObserver", "filter", "x", "optionalRemove", "length", "status", "remove", "continue", "retryer", "execute", "executeMutation", "createRetryer", "fn", "mutationFn", "Promise", "reject", "variables", "onFail", "failureCount", "error", "onPause", "onContinue", "retry", "retry<PERSON><PERSON><PERSON>", "networkMode", "promise", "restored", "onMutate", "context", "data", "onSuccess", "onSettled", "onError", "process", "env", "NODE_ENV", "undefined", "action", "reducer", "failureReason", "isPaused", "canFetch", "notify<PERSON><PERSON>ger", "batch", "for<PERSON>ach", "onMutationUpdate"], "mappings": ";;;;;AAkFA;AAEO,MAAMA,QAAN,SAKGC,SALH,CAKa;EAWlBC,WAAW,CAACC,MAAD,EAA8D;AACvE,IAAA,KAAA,EAAA,CAAA;AAEA,IAAA,IAAA,CAAKC,cAAL,GAAsBD,MAAM,CAACC,cAA7B,CAAA;AACA,IAAA,IAAA,CAAKC,UAAL,GAAkBF,MAAM,CAACE,UAAzB,CAAA;AACA,IAAA,IAAA,CAAKC,aAAL,GAAqBH,MAAM,CAACG,aAA5B,CAAA;AACA,IAAA,IAAA,CAAKC,MAAL,GAAcJ,MAAM,CAACI,MAAP,IAAiBC,aAA/B,CAAA;IACA,IAAKC,CAAAA,SAAL,GAAiB,EAAjB,CAAA;AACA,IAAA,IAAA,CAAKC,KAAL,GAAaP,MAAM,CAACO,KAAP,IAAgBC,eAAe,EAA5C,CAAA;AAEA,IAAA,IAAA,CAAKC,UAAL,CAAgBT,MAAM,CAACU,OAAvB,CAAA,CAAA;AACA,IAAA,IAAA,CAAKC,UAAL,EAAA,CAAA;AACD,GAAA;;EAEDF,UAAU,CACRC,OADQ,EAEF;AACN,IAAA,IAAA,CAAKA,OAAL,GAAe,EAAE,GAAG,KAAKT,cAAV;MAA0B,GAAGS,OAAAA;KAA5C,CAAA;AAEA,IAAA,IAAA,CAAKE,eAAL,CAAqB,IAAKF,CAAAA,OAAL,CAAaG,SAAlC,CAAA,CAAA;AACD,GAAA;;AAEO,EAAA,IAAJC,IAAI,GAA6B;IACnC,OAAO,IAAA,CAAKJ,OAAL,CAAaI,IAApB,CAAA;AACD,GAAA;;EAEDC,QAAQ,CAACR,KAAD,EAAkE;AACxE,IAAA,IAAA,CAAKS,QAAL,CAAc;AAAEC,MAAAA,IAAI,EAAE,UAAR;AAAoBV,MAAAA,KAAAA;KAAlC,CAAA,CAAA;AACD,GAAA;;EAEDW,WAAW,CAACC,QAAD,EAAuD;IAChE,IAAI,CAAC,KAAKb,SAAL,CAAec,QAAf,CAAwBD,QAAxB,CAAL,EAAwC;AACtC,MAAA,IAAA,CAAKb,SAAL,CAAee,IAAf,CAAoBF,QAApB,EADsC;;AAItC,MAAA,IAAA,CAAKG,cAAL,EAAA,CAAA;MAEA,IAAKnB,CAAAA,aAAL,CAAmBoB,MAAnB,CAA0B;AACxBN,QAAAA,IAAI,EAAE,eADkB;AAExBO,QAAAA,QAAQ,EAAE,IAFc;AAGxBL,QAAAA,QAAAA;OAHF,CAAA,CAAA;AAKD,KAAA;AACF,GAAA;;EAEDM,cAAc,CAACN,QAAD,EAAuD;AACnE,IAAA,IAAA,CAAKb,SAAL,GAAiB,IAAKA,CAAAA,SAAL,CAAeoB,MAAf,CAAuBC,CAAD,IAAOA,CAAC,KAAKR,QAAnC,CAAjB,CAAA;AAEA,IAAA,IAAA,CAAKR,UAAL,EAAA,CAAA;IAEA,IAAKR,CAAAA,aAAL,CAAmBoB,MAAnB,CAA0B;AACxBN,MAAAA,IAAI,EAAE,iBADkB;AAExBO,MAAAA,QAAQ,EAAE,IAFc;AAGxBL,MAAAA,QAAAA;KAHF,CAAA,CAAA;AAKD,GAAA;;AAESS,EAAAA,cAAc,GAAG;AACzB,IAAA,IAAI,CAAC,IAAA,CAAKtB,SAAL,CAAeuB,MAApB,EAA4B;AAC1B,MAAA,IAAI,KAAKtB,KAAL,CAAWuB,MAAX,KAAsB,SAA1B,EAAqC;AACnC,QAAA,IAAA,CAAKnB,UAAL,EAAA,CAAA;AACD,OAFD,MAEO;AACL,QAAA,IAAA,CAAKR,aAAL,CAAmB4B,MAAnB,CAA0B,IAA1B,CAAA,CAAA;AACD,OAAA;AACF,KAAA;AACF,GAAA;;AAEDC,EAAAA,QAAQ,GAAqB;AAAA,IAAA,IAAA,qBAAA,EAAA,aAAA,CAAA;;IAC3B,OAAO,CAAA,qBAAA,GAAA,CAAA,aAAA,GAAA,IAAA,CAAKC,OAAZ,KAAO,IAAA,GAAA,KAAA,CAAA,GAAA,aAAA,CAAcD,QAAd,EAAP,KAAA,IAAA,GAAA,qBAAA,GAAmC,IAAKE,CAAAA,OAAL,EAAnC,CAAA;AACD,GAAA;;AAEY,EAAA,MAAPA,OAAO,GAAmB;IAC9B,MAAMC,eAAe,GAAG,MAAM;AAAA,MAAA,IAAA,mBAAA,CAAA;;MAC5B,IAAKF,CAAAA,OAAL,GAAeG,aAAa,CAAC;AAC3BC,QAAAA,EAAE,EAAE,MAAM;AACR,UAAA,IAAI,CAAC,IAAA,CAAK3B,OAAL,CAAa4B,UAAlB,EAA8B;AAC5B,YAAA,OAAOC,OAAO,CAACC,MAAR,CAAe,qBAAf,CAAP,CAAA;AACD,WAAA;;UACD,OAAO,IAAA,CAAK9B,OAAL,CAAa4B,UAAb,CAAwB,IAAK/B,CAAAA,KAAL,CAAWkC,SAAnC,CAAP,CAAA;SALyB;AAO3BC,QAAAA,MAAM,EAAE,CAACC,YAAD,EAAeC,KAAf,KAAyB;AAC/B,UAAA,IAAA,CAAK5B,QAAL,CAAc;AAAEC,YAAAA,IAAI,EAAE,QAAR;YAAkB0B,YAAlB;AAAgCC,YAAAA,KAAAA;WAA9C,CAAA,CAAA;SARyB;AAU3BC,QAAAA,OAAO,EAAE,MAAM;AACb,UAAA,IAAA,CAAK7B,QAAL,CAAc;AAAEC,YAAAA,IAAI,EAAE,OAAA;WAAtB,CAAA,CAAA;SAXyB;AAa3B6B,QAAAA,UAAU,EAAE,MAAM;AAChB,UAAA,IAAA,CAAK9B,QAAL,CAAc;AAAEC,YAAAA,IAAI,EAAE,UAAA;WAAtB,CAAA,CAAA;SAdyB;AAgB3B8B,QAAAA,KAAK,yBAAE,IAAKrC,CAAAA,OAAL,CAAaqC,KAAf,kCAAwB,CAhBF;AAiB3BC,QAAAA,UAAU,EAAE,IAAA,CAAKtC,OAAL,CAAasC,UAjBE;QAkB3BC,WAAW,EAAE,IAAKvC,CAAAA,OAAL,CAAauC,WAAAA;AAlBC,OAAD,CAA5B,CAAA;MAqBA,OAAO,IAAA,CAAKhB,OAAL,CAAaiB,OAApB,CAAA;KAtBF,CAAA;;AAyBA,IAAA,MAAMC,QAAQ,GAAG,IAAA,CAAK5C,KAAL,CAAWuB,MAAX,KAAsB,SAAvC,CAAA;;IACA,IAAI;AAAA,MAAA,IAAA,sBAAA,EAAA,sBAAA,EAAA,qBAAA,EAAA,cAAA,EAAA,sBAAA,EAAA,sBAAA,EAAA,qBAAA,EAAA,cAAA,CAAA;;MACF,IAAI,CAACqB,QAAL,EAAe;AAAA,QAAA,IAAA,qBAAA,EAAA,sBAAA,EAAA,qBAAA,EAAA,aAAA,CAAA;;AACb,QAAA,IAAA,CAAKnC,QAAL,CAAc;AAAEC,UAAAA,IAAI,EAAE,SAAR;UAAmBwB,SAAS,EAAE,IAAK/B,CAAAA,OAAL,CAAa+B,SAAAA;AAA3C,SAAd,EADa;;AAGb,QAAA,OAAA,CAAA,qBAAA,GAAM,CAAKtC,sBAAAA,GAAAA,IAAAA,CAAAA,aAAL,CAAmBH,MAAnB,EAA0BoD,QAAhC,KAAA,IAAA,GAAA,KAAA,CAAA,GAAM,qBACJ,CAAA,IAAA,CAAA,sBAAA,EAAA,IAAA,CAAK7C,KAAL,CAAWkC,SADP,EAEJ,IAFI,CAAN,CAAA,CAAA;AAIA,QAAA,MAAMY,OAAO,GAAG,OAAM,CAAA,qBAAA,GAAA,CAAA,aAAA,GAAA,IAAA,CAAK3C,OAAL,EAAa0C,QAAnB,KAAM,IAAA,GAAA,KAAA,CAAA,GAAA,qBAAA,CAAA,IAAA,CAAA,aAAA,EAAwB,IAAK7C,CAAAA,KAAL,CAAWkC,SAAnC,CAAN,CAAhB,CAAA;;AACA,QAAA,IAAIY,OAAO,KAAK,IAAA,CAAK9C,KAAL,CAAW8C,OAA3B,EAAoC;AAClC,UAAA,IAAA,CAAKrC,QAAL,CAAc;AACZC,YAAAA,IAAI,EAAE,SADM;YAEZoC,OAFY;YAGZZ,SAAS,EAAE,IAAKlC,CAAAA,KAAL,CAAWkC,SAAAA;WAHxB,CAAA,CAAA;AAKD,SAAA;AACF,OAAA;;AACD,MAAA,MAAMa,IAAI,GAAG,MAAMnB,eAAe,EAAlC,CAjBE;;MAoBF,OAAM,CAAA,sBAAA,GAAA,CAAA,sBAAA,GAAA,IAAA,CAAKhC,aAAL,CAAmBH,MAAnB,EAA0BuD,SAAhC,KAAA,IAAA,GAAA,KAAA,CAAA,GAAM,oDACJD,IADI,EAEJ,KAAK/C,KAAL,CAAWkC,SAFP,EAGJ,IAAA,CAAKlC,KAAL,CAAW8C,OAHP,EAIJ,IAJI,CAAN,CAAA,CAAA;AAOA,MAAA,OAAA,CAAA,qBAAA,GAAM,uBAAK3C,OAAL,EAAa6C,SAAnB,KAAM,IAAA,GAAA,KAAA,CAAA,GAAA,qBAAA,CAAA,IAAA,CAAA,cAAA,EACJD,IADI,EAEJ,IAAA,CAAK/C,KAAL,CAAWkC,SAFP,EAGJ,IAAKlC,CAAAA,KAAL,CAAW8C,OAHP,CAAN,EA3BE;;MAkCF,OAAM,CAAA,sBAAA,GAAA,CAAA,sBAAA,GAAA,IAAA,CAAKlD,aAAL,CAAmBH,MAAnB,EAA0BwD,SAAhC,KAAA,IAAA,GAAA,KAAA,CAAA,GAAM,sBACJF,CAAAA,IAAAA,CAAAA,sBAAAA,EAAAA,IADI,EAEJ,IAFI,EAGJ,IAAK/C,CAAAA,KAAL,CAAWkC,SAHP,EAIJ,IAAA,CAAKlC,KAAL,CAAW8C,OAJP,EAKJ,IALI,CAAN,CAAA,CAAA;AAQA,MAAA,OAAA,CAAA,qBAAA,GAAM,uBAAK3C,OAAL,EAAa8C,SAAnB,KAAM,IAAA,GAAA,KAAA,CAAA,GAAA,qBAAA,CAAA,IAAA,CAAA,cAAA,EACJF,IADI,EAEJ,IAFI,EAGJ,IAAK/C,CAAAA,KAAL,CAAWkC,SAHP,EAIJ,KAAKlC,KAAL,CAAW8C,OAJP,CAAN,CAAA,CAAA;AAOA,MAAA,IAAA,CAAKrC,QAAL,CAAc;AAAEC,QAAAA,IAAI,EAAE,SAAR;AAAmBqC,QAAAA,IAAAA;OAAjC,CAAA,CAAA;AACA,MAAA,OAAOA,IAAP,CAAA;KAlDF,CAmDE,OAAOV,KAAP,EAAc;MACd,IAAI;AAAA,QAAA,IAAA,sBAAA,EAAA,sBAAA,EAAA,qBAAA,EAAA,cAAA,EAAA,sBAAA,EAAA,uBAAA,EAAA,sBAAA,EAAA,cAAA,CAAA;;AACF;QACA,OAAM,CAAA,sBAAA,GAAA,CAAA,sBAAA,GAAA,IAAA,CAAKzC,aAAL,CAAmBH,MAAnB,EAA0ByD,OAAhC,KAAA,IAAA,GAAA,KAAA,CAAA,GAAM,oDACJb,KADI,EAEJ,KAAKrC,KAAL,CAAWkC,SAFP,EAGJ,IAAA,CAAKlC,KAAL,CAAW8C,OAHP,EAIJ,IAJI,CAAN,CAAA,CAAA;;AAOA,QAAA,IAAIK,OAAO,CAACC,GAAR,CAAYC,QAAZ,KAAyB,YAA7B,EAA2C;AACzC,UAAA,IAAA,CAAKxD,MAAL,CAAYwC,KAAZ,CAAkBA,KAAlB,CAAA,CAAA;AACD,SAAA;;AAED,QAAA,OAAA,CAAA,qBAAA,GAAM,uBAAKlC,OAAL,EAAa+C,OAAnB,KAAM,IAAA,GAAA,KAAA,CAAA,GAAA,qBAAA,CAAA,IAAA,CAAA,cAAA,EACJb,KADI,EAEJ,IAAA,CAAKrC,KAAL,CAAWkC,SAFP,EAGJ,IAAKlC,CAAAA,KAAL,CAAW8C,OAHP,CAAN,EAbE;;QAoBF,OAAM,CAAA,sBAAA,GAAA,CAAA,uBAAA,GAAA,IAAA,CAAKlD,aAAL,CAAmBH,MAAnB,EAA0BwD,SAAhC,KAAA,IAAA,GAAA,KAAA,CAAA,GAAM,sBACJK,CAAAA,IAAAA,CAAAA,uBAAAA,EAAAA,SADI,EAEJjB,KAFI,EAGJ,IAAKrC,CAAAA,KAAL,CAAWkC,SAHP,EAIJ,IAAA,CAAKlC,KAAL,CAAW8C,OAJP,EAKJ,IALI,CAAN,CAAA,CAAA;AAQA,QAAA,OAAA,CAAA,sBAAA,GAAM,uBAAK3C,OAAL,EAAa8C,SAAnB,KAAM,IAAA,GAAA,KAAA,CAAA,GAAA,sBAAA,CAAA,IAAA,CAAA,cAAA,EACJK,SADI,EAEJjB,KAFI,EAGJ,IAAKrC,CAAAA,KAAL,CAAWkC,SAHP,EAIJ,KAAKlC,KAAL,CAAW8C,OAJP,CAAN,CAAA,CAAA;AAMA,QAAA,MAAMT,KAAN,CAAA;AACD,OAnCD,SAmCU;AACR,QAAA,IAAA,CAAK5B,QAAL,CAAc;AAAEC,UAAAA,IAAI,EAAE,OAAR;AAAiB2B,UAAAA,KAAK,EAAEA,KAAAA;SAAtC,CAAA,CAAA;AACD,OAAA;AACF,KAAA;AACF,GAAA;;EAEO5B,QAAQ,CAAC8C,MAAD,EAA4D;IAC1E,MAAMC,OAAO,GACXxD,KADc,IAEyC;MACvD,QAAQuD,MAAM,CAAC7C,IAAf;AACE,QAAA,KAAK,QAAL;UACE,OAAO,EACL,GAAGV,KADE;YAELoC,YAAY,EAAEmB,MAAM,CAACnB,YAFhB;YAGLqB,aAAa,EAAEF,MAAM,CAAClB,KAAAA;WAHxB,CAAA;;AAKF,QAAA,KAAK,OAAL;UACE,OAAO,EACL,GAAGrC,KADE;AAEL0D,YAAAA,QAAQ,EAAE,IAAA;WAFZ,CAAA;;AAIF,QAAA,KAAK,UAAL;UACE,OAAO,EACL,GAAG1D,KADE;AAEL0D,YAAAA,QAAQ,EAAE,KAAA;WAFZ,CAAA;;AAIF,QAAA,KAAK,SAAL;UACE,OAAO,EACL,GAAG1D,KADE;YAEL8C,OAAO,EAAES,MAAM,CAACT,OAFX;AAGLC,YAAAA,IAAI,EAAEO,SAHD;AAILlB,YAAAA,YAAY,EAAE,CAJT;AAKLqB,YAAAA,aAAa,EAAE,IALV;AAMLpB,YAAAA,KAAK,EAAE,IANF;YAOLqB,QAAQ,EAAE,CAACC,QAAQ,CAAC,KAAKxD,OAAL,CAAauC,WAAd,CAPd;AAQLnB,YAAAA,MAAM,EAAE,SARH;YASLW,SAAS,EAAEqB,MAAM,CAACrB,SAAAA;WATpB,CAAA;;AAWF,QAAA,KAAK,SAAL;UACE,OAAO,EACL,GAAGlC,KADE;YAEL+C,IAAI,EAAEQ,MAAM,CAACR,IAFR;AAGLX,YAAAA,YAAY,EAAE,CAHT;AAILqB,YAAAA,aAAa,EAAE,IAJV;AAKLpB,YAAAA,KAAK,EAAE,IALF;AAMLd,YAAAA,MAAM,EAAE,SANH;AAOLmC,YAAAA,QAAQ,EAAE,KAAA;WAPZ,CAAA;;AASF,QAAA,KAAK,OAAL;UACE,OAAO,EACL,GAAG1D,KADE;AAEL+C,YAAAA,IAAI,EAAEO,SAFD;YAGLjB,KAAK,EAAEkB,MAAM,CAAClB,KAHT;AAILD,YAAAA,YAAY,EAAEpC,KAAK,CAACoC,YAAN,GAAqB,CAJ9B;YAKLqB,aAAa,EAAEF,MAAM,CAAClB,KALjB;AAMLqB,YAAAA,QAAQ,EAAE,KANL;AAOLnC,YAAAA,MAAM,EAAE,OAAA;WAPV,CAAA;;AASF,QAAA,KAAK,UAAL;UACE,OAAO,EACL,GAAGvB,KADE;AAEL,YAAA,GAAGuD,MAAM,CAACvD,KAAAA;WAFZ,CAAA;AAlDJ,OAAA;KAHF,CAAA;;AA2DA,IAAA,IAAA,CAAKA,KAAL,GAAawD,OAAO,CAAC,IAAA,CAAKxD,KAAN,CAApB,CAAA;IAEA4D,aAAa,CAACC,KAAd,CAAoB,MAAM;AACxB,MAAA,IAAA,CAAK9D,SAAL,CAAe+D,OAAf,CAAwBlD,QAAD,IAAc;QACnCA,QAAQ,CAACmD,gBAAT,CAA0BR,MAA1B,CAAA,CAAA;OADF,CAAA,CAAA;MAGA,IAAK3D,CAAAA,aAAL,CAAmBoB,MAAnB,CAA0B;AACxBC,QAAAA,QAAQ,EAAE,IADc;AAExBP,QAAAA,IAAI,EAAE,SAFkB;AAGxB6C,QAAAA,MAAAA;OAHF,CAAA,CAAA;KAJF,CAAA,CAAA;AAUD,GAAA;;AAlRiB,CAAA;AAqRb,SAAStD,eAAT,GAKiD;EACtD,OAAO;AACL6C,IAAAA,OAAO,EAAEQ,SADJ;AAELP,IAAAA,IAAI,EAAEO,SAFD;AAGLjB,IAAAA,KAAK,EAAE,IAHF;AAILD,IAAAA,YAAY,EAAE,CAJT;AAKLqB,IAAAA,aAAa,EAAE,IALV;AAMLC,IAAAA,QAAQ,EAAE,KANL;AAOLnC,IAAAA,MAAM,EAAE,MAPH;AAQLW,IAAAA,SAAS,EAAEoB,SAAAA;GARb,CAAA;AAUD;;;;"}