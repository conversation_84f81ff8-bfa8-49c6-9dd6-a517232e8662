{"version": 3, "file": "retryer.js", "sources": ["../../src/retryer.ts"], "sourcesContent": ["import { focusManager } from './focusManager'\nimport { onlineManager } from './onlineManager'\nimport { sleep } from './utils'\nimport type { CancelOptions, NetworkMode } from './types'\n\n// TYPES\n\ninterface RetryerConfig<TData = unknown, TError = unknown> {\n  fn: () => TData | Promise<TData>\n  abort?: () => void\n  onError?: (error: TError) => void\n  onSuccess?: (data: TData) => void\n  onFail?: (failureCount: number, error: TError) => void\n  onPause?: () => void\n  onContinue?: () => void\n  retry?: RetryValue<TError>\n  retryDelay?: RetryDelayValue<TError>\n  networkMode: NetworkMode | undefined\n}\n\nexport interface Retryer<TData = unknown> {\n  promise: Promise<TData>\n  cancel: (cancelOptions?: CancelOptions) => void\n  continue: () => Promise<unknown>\n  cancelRetry: () => void\n  continueRetry: () => void\n}\n\nexport type RetryValue<TError> = boolean | number | ShouldRetryFunction<TError>\n\ntype ShouldRetryFunction<TError> = (\n  failureCount: number,\n  error: TError,\n) => boolean\n\nexport type RetryDelayValue<TError> = number | RetryDelayFunction<TError>\n\ntype RetryDelayFunction<TError = unknown> = (\n  failureCount: number,\n  error: TError,\n) => number\n\nfunction defaultRetryDelay(failureCount: number) {\n  return Math.min(1000 * 2 ** failureCount, 30000)\n}\n\nexport function canFetch(networkMode: NetworkMode | undefined): boolean {\n  return (networkMode ?? 'online') === 'online'\n    ? onlineManager.isOnline()\n    : true\n}\n\nexport class CancelledError {\n  revert?: boolean\n  silent?: boolean\n  constructor(options?: CancelOptions) {\n    this.revert = options?.revert\n    this.silent = options?.silent\n  }\n}\n\nexport function isCancelledError(value: any): value is CancelledError {\n  return value instanceof CancelledError\n}\n\nexport function createRetryer<TData = unknown, TError = unknown>(\n  config: RetryerConfig<TData, TError>,\n): Retryer<TData> {\n  let isRetryCancelled = false\n  let failureCount = 0\n  let isResolved = false\n  let continueFn: ((value?: unknown) => boolean) | undefined\n  let promiseResolve: (data: TData) => void\n  let promiseReject: (error: TError) => void\n\n  const promise = new Promise<TData>((outerResolve, outerReject) => {\n    promiseResolve = outerResolve\n    promiseReject = outerReject\n  })\n\n  const cancel = (cancelOptions?: CancelOptions): void => {\n    if (!isResolved) {\n      reject(new CancelledError(cancelOptions))\n\n      config.abort?.()\n    }\n  }\n  const cancelRetry = () => {\n    isRetryCancelled = true\n  }\n\n  const continueRetry = () => {\n    isRetryCancelled = false\n  }\n\n  const shouldPause = () =>\n    !focusManager.isFocused() ||\n    (config.networkMode !== 'always' && !onlineManager.isOnline())\n\n  const resolve = (value: any) => {\n    if (!isResolved) {\n      isResolved = true\n      config.onSuccess?.(value)\n      continueFn?.()\n      promiseResolve(value)\n    }\n  }\n\n  const reject = (value: any) => {\n    if (!isResolved) {\n      isResolved = true\n      config.onError?.(value)\n      continueFn?.()\n      promiseReject(value)\n    }\n  }\n\n  const pause = () => {\n    return new Promise((continueResolve) => {\n      continueFn = (value) => {\n        const canContinue = isResolved || !shouldPause()\n        if (canContinue) {\n          continueResolve(value)\n        }\n        return canContinue\n      }\n      config.onPause?.()\n    }).then(() => {\n      continueFn = undefined\n      if (!isResolved) {\n        config.onContinue?.()\n      }\n    })\n  }\n\n  // Create loop function\n  const run = () => {\n    // Do nothing if already resolved\n    if (isResolved) {\n      return\n    }\n\n    let promiseOrValue: any\n\n    // Execute query\n    try {\n      promiseOrValue = config.fn()\n    } catch (error) {\n      promiseOrValue = Promise.reject(error)\n    }\n\n    Promise.resolve(promiseOrValue)\n      .then(resolve)\n      .catch((error) => {\n        // Stop if the fetch is already resolved\n        if (isResolved) {\n          return\n        }\n\n        // Do we need to retry the request?\n        const retry = config.retry ?? 3\n        const retryDelay = config.retryDelay ?? defaultRetryDelay\n        const delay =\n          typeof retryDelay === 'function'\n            ? retryDelay(failureCount, error)\n            : retryDelay\n        const shouldRetry =\n          retry === true ||\n          (typeof retry === 'number' && failureCount < retry) ||\n          (typeof retry === 'function' && retry(failureCount, error))\n\n        if (isRetryCancelled || !shouldRetry) {\n          // We are done if the query does not need to be retried\n          reject(error)\n          return\n        }\n\n        failureCount++\n\n        // Notify on fail\n        config.onFail?.(failureCount, error)\n\n        // Delay\n        sleep(delay)\n          // Pause if the document is not visible or when the device is offline\n          .then(() => {\n            if (shouldPause()) {\n              return pause()\n            }\n            return\n          })\n          .then(() => {\n            if (isRetryCancelled) {\n              reject(error)\n            } else {\n              run()\n            }\n          })\n      })\n  }\n\n  // Start loop\n  if (canFetch(config.networkMode)) {\n    run()\n  } else {\n    pause().then(run)\n  }\n\n  return {\n    promise,\n    cancel,\n    continue: () => {\n      const didContinue = continueFn?.()\n      return didContinue ? promise : Promise.resolve()\n    },\n    cancelRetry,\n    continueRetry,\n  }\n}\n"], "names": ["defaultRetryDelay", "failureCount", "Math", "min", "canFetch", "networkMode", "onlineManager", "isOnline", "CancelledError", "constructor", "options", "revert", "silent", "isCancelledError", "value", "createRetryer", "config", "isRetryCancelled", "isResolved", "continueFn", "promiseResolve", "promiseReject", "promise", "Promise", "outerResolve", "outerReject", "cancel", "cancelOptions", "reject", "abort", "cancelRetry", "continueRetry", "shouldP<PERSON>e", "focusManager", "isFocused", "resolve", "onSuccess", "onError", "pause", "continueResolve", "canContinue", "onPause", "then", "undefined", "onContinue", "run", "promiseOrValue", "fn", "error", "catch", "retry", "retry<PERSON><PERSON><PERSON>", "delay", "shouldRetry", "onFail", "sleep", "continue", "didContinue"], "mappings": ";;;;;;;;AA0CA,SAASA,iBAAT,CAA2BC,YAA3B,EAAiD;EAC/C,OAAOC,IAAI,CAACC,GAAL,CAAS,OAAO,CAAKF,IAAAA,YAArB,EAAmC,KAAnC,CAAP,CAAA;AACD,CAAA;;AAEM,SAASG,QAAT,CAAkBC,WAAlB,EAAiE;AACtE,EAAA,OAAO,CAACA,WAAD,IAACA,IAAAA,GAAAA,WAAD,GAAgB,QAAhB,MAA8B,QAA9B,GACHC,2BAAa,CAACC,QAAd,EADG,GAEH,IAFJ,CAAA;AAGD,CAAA;AAEM,MAAMC,cAAN,CAAqB;EAG1BC,WAAW,CAACC,OAAD,EAA0B;AACnC,IAAA,IAAA,CAAKC,MAAL,GAAcD,OAAd,IAAcA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,OAAO,CAAEC,MAAvB,CAAA;AACA,IAAA,IAAA,CAAKC,MAAL,GAAcF,OAAd,IAAcA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,OAAO,CAAEE,MAAvB,CAAA;AACD,GAAA;;AANyB,CAAA;AASrB,SAASC,gBAAT,CAA0BC,KAA1B,EAA+D;EACpE,OAAOA,KAAK,YAAYN,cAAxB,CAAA;AACD,CAAA;AAEM,SAASO,aAAT,CACLC,MADK,EAEW;EAChB,IAAIC,gBAAgB,GAAG,KAAvB,CAAA;EACA,IAAIhB,YAAY,GAAG,CAAnB,CAAA;EACA,IAAIiB,UAAU,GAAG,KAAjB,CAAA;AACA,EAAA,IAAIC,UAAJ,CAAA;AACA,EAAA,IAAIC,cAAJ,CAAA;AACA,EAAA,IAAIC,aAAJ,CAAA;EAEA,MAAMC,OAAO,GAAG,IAAIC,OAAJ,CAAmB,CAACC,YAAD,EAAeC,WAAf,KAA+B;AAChEL,IAAAA,cAAc,GAAGI,YAAjB,CAAA;AACAH,IAAAA,aAAa,GAAGI,WAAhB,CAAA;AACD,GAHe,CAAhB,CAAA;;EAKA,MAAMC,MAAM,GAAIC,aAAD,IAAyC;IACtD,IAAI,CAACT,UAAL,EAAiB;AACfU,MAAAA,MAAM,CAAC,IAAIpB,cAAJ,CAAmBmB,aAAnB,CAAD,CAAN,CAAA;AAEAX,MAAAA,MAAM,CAACa,KAAP,IAAAb,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,MAAM,CAACa,KAAP,EAAA,CAAA;AACD,KAAA;GALH,CAAA;;EAOA,MAAMC,WAAW,GAAG,MAAM;AACxBb,IAAAA,gBAAgB,GAAG,IAAnB,CAAA;GADF,CAAA;;EAIA,MAAMc,aAAa,GAAG,MAAM;AAC1Bd,IAAAA,gBAAgB,GAAG,KAAnB,CAAA;GADF,CAAA;;AAIA,EAAA,MAAMe,WAAW,GAAG,MAClB,CAACC,yBAAY,CAACC,SAAb,EAAD,IACClB,MAAM,CAACX,WAAP,KAAuB,QAAvB,IAAmC,CAACC,2BAAa,CAACC,QAAd,EAFvC,CAAA;;EAIA,MAAM4B,OAAO,GAAIrB,KAAD,IAAgB;IAC9B,IAAI,CAACI,UAAL,EAAiB;AACfA,MAAAA,UAAU,GAAG,IAAb,CAAA;AACAF,MAAAA,MAAM,CAACoB,SAAP,IAAA,IAAA,GAAA,KAAA,CAAA,GAAApB,MAAM,CAACoB,SAAP,CAAmBtB,KAAnB,CAAA,CAAA;MACAK,UAAU,IAAA,IAAV,YAAAA,UAAU,EAAA,CAAA;MACVC,cAAc,CAACN,KAAD,CAAd,CAAA;AACD,KAAA;GANH,CAAA;;EASA,MAAMc,MAAM,GAAId,KAAD,IAAgB;IAC7B,IAAI,CAACI,UAAL,EAAiB;AACfA,MAAAA,UAAU,GAAG,IAAb,CAAA;AACAF,MAAAA,MAAM,CAACqB,OAAP,IAAA,IAAA,GAAA,KAAA,CAAA,GAAArB,MAAM,CAACqB,OAAP,CAAiBvB,KAAjB,CAAA,CAAA;MACAK,UAAU,IAAA,IAAV,YAAAA,UAAU,EAAA,CAAA;MACVE,aAAa,CAACP,KAAD,CAAb,CAAA;AACD,KAAA;GANH,CAAA;;EASA,MAAMwB,KAAK,GAAG,MAAM;AAClB,IAAA,OAAO,IAAIf,OAAJ,CAAagB,eAAD,IAAqB;MACtCpB,UAAU,GAAIL,KAAD,IAAW;AACtB,QAAA,MAAM0B,WAAW,GAAGtB,UAAU,IAAI,CAACc,WAAW,EAA9C,CAAA;;AACA,QAAA,IAAIQ,WAAJ,EAAiB;UACfD,eAAe,CAACzB,KAAD,CAAf,CAAA;AACD,SAAA;;AACD,QAAA,OAAO0B,WAAP,CAAA;OALF,CAAA;;AAOAxB,MAAAA,MAAM,CAACyB,OAAP,IAAAzB,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,MAAM,CAACyB,OAAP,EAAA,CAAA;KARK,CAAA,CASJC,IATI,CASC,MAAM;AACZvB,MAAAA,UAAU,GAAGwB,SAAb,CAAA;;MACA,IAAI,CAACzB,UAAL,EAAiB;AACfF,QAAAA,MAAM,CAAC4B,UAAP,IAAA5B,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,MAAM,CAAC4B,UAAP,EAAA,CAAA;AACD,OAAA;AACF,KAdM,CAAP,CAAA;AAeD,GAhBD,CAlDgB;;;EAqEhB,MAAMC,GAAG,GAAG,MAAM;AAChB;AACA,IAAA,IAAI3B,UAAJ,EAAgB;AACd,MAAA,OAAA;AACD,KAAA;;IAED,IAAI4B,cAAJ,CANgB;;IAShB,IAAI;AACFA,MAAAA,cAAc,GAAG9B,MAAM,CAAC+B,EAAP,EAAjB,CAAA;KADF,CAEE,OAAOC,KAAP,EAAc;AACdF,MAAAA,cAAc,GAAGvB,OAAO,CAACK,MAAR,CAAeoB,KAAf,CAAjB,CAAA;AACD,KAAA;;AAEDzB,IAAAA,OAAO,CAACY,OAAR,CAAgBW,cAAhB,CACGJ,CAAAA,IADH,CACQP,OADR,CAEGc,CAAAA,KAFH,CAEUD,KAAD,IAAW;AAAA,MAAA,IAAA,aAAA,EAAA,kBAAA,CAAA;;AAChB;AACA,MAAA,IAAI9B,UAAJ,EAAgB;AACd,QAAA,OAAA;AACD,OAJe;;;AAOhB,MAAA,MAAMgC,KAAK,GAAGlC,CAAAA,aAAAA,GAAAA,MAAM,CAACkC,KAAV,4BAAmB,CAA9B,CAAA;AACA,MAAA,MAAMC,UAAU,GAAGnC,CAAAA,kBAAAA,GAAAA,MAAM,CAACmC,UAAV,iCAAwBnD,iBAAxC,CAAA;AACA,MAAA,MAAMoD,KAAK,GACT,OAAOD,UAAP,KAAsB,UAAtB,GACIA,UAAU,CAAClD,YAAD,EAAe+C,KAAf,CADd,GAEIG,UAHN,CAAA;MAIA,MAAME,WAAW,GACfH,KAAK,KAAK,IAAV,IACC,OAAOA,KAAP,KAAiB,QAAjB,IAA6BjD,YAAY,GAAGiD,KAD7C,IAEC,OAAOA,KAAP,KAAiB,UAAjB,IAA+BA,KAAK,CAACjD,YAAD,EAAe+C,KAAf,CAHvC,CAAA;;AAKA,MAAA,IAAI/B,gBAAgB,IAAI,CAACoC,WAAzB,EAAsC;AACpC;QACAzB,MAAM,CAACoB,KAAD,CAAN,CAAA;AACA,QAAA,OAAA;AACD,OAAA;;AAED/C,MAAAA,YAAY,GAxBI;;MA2BhBe,MAAM,CAACsC,MAAP,IAAA,IAAA,GAAA,KAAA,CAAA,GAAAtC,MAAM,CAACsC,MAAP,CAAgBrD,YAAhB,EAA8B+C,KAA9B,CAAA,CA3BgB;;MA8BhBO,WAAK,CAACH,KAAD,CAAL;OAEGV,IAFH,CAEQ,MAAM;QACV,IAAIV,WAAW,EAAf,EAAmB;AACjB,UAAA,OAAOM,KAAK,EAAZ,CAAA;AACD,SAAA;;AACD,QAAA,OAAA;OANJ,CAAA,CAQGI,IARH,CAQQ,MAAM;AACV,QAAA,IAAIzB,gBAAJ,EAAsB;UACpBW,MAAM,CAACoB,KAAD,CAAN,CAAA;AACD,SAFD,MAEO;UACLH,GAAG,EAAA,CAAA;AACJ,SAAA;OAbL,CAAA,CAAA;KAhCJ,CAAA,CAAA;AAgDD,GA/DD,CArEgB;;;AAuIhB,EAAA,IAAIzC,QAAQ,CAACY,MAAM,CAACX,WAAR,CAAZ,EAAkC;IAChCwC,GAAG,EAAA,CAAA;AACJ,GAFD,MAEO;IACLP,KAAK,EAAA,CAAGI,IAAR,CAAaG,GAAb,CAAA,CAAA;AACD,GAAA;;EAED,OAAO;IACLvB,OADK;IAELI,MAFK;AAGL8B,IAAAA,QAAQ,EAAE,MAAM;AACd,MAAA,MAAMC,WAAW,GAAGtC,UAAH,IAAA,IAAA,GAAA,KAAA,CAAA,GAAGA,UAAU,EAA9B,CAAA;AACA,MAAA,OAAOsC,WAAW,GAAGnC,OAAH,GAAaC,OAAO,CAACY,OAAR,EAA/B,CAAA;KALG;IAOLL,WAPK;AAQLC,IAAAA,aAAAA;GARF,CAAA;AAUD;;;;;;;"}