{"version": 3, "file": "removable.esm.js", "sources": ["../../src/removable.ts"], "sourcesContent": ["import { isServer, isValidTimeout } from './utils'\n\nexport abstract class Removable {\n  cacheTime!: number\n  private gcTimeout?: ReturnType<typeof setTimeout>\n\n  destroy(): void {\n    this.clearGcTimeout()\n  }\n\n  protected scheduleGc(): void {\n    this.clearGcTimeout()\n\n    if (isValidTimeout(this.cacheTime)) {\n      this.gcTimeout = setTimeout(() => {\n        this.optionalRemove()\n      }, this.cacheTime)\n    }\n  }\n\n  protected updateCacheTime(newCacheTime: number | undefined): void {\n    // Default to 5 minutes (Infinity for server-side) if no cache time is set\n    this.cacheTime = Math.max(\n      this.cacheTime || 0,\n      newCacheTime ?? (isServer ? Infinity : 5 * 60 * 1000),\n    )\n  }\n\n  protected clearGcTimeout() {\n    if (this.gcTimeout) {\n      clearTimeout(this.gcTimeout)\n      this.gcTimeout = undefined\n    }\n  }\n\n  protected abstract optionalRemove(): void\n}\n"], "names": ["Removable", "destroy", "clearGcTimeout", "scheduleGc", "isValidTimeout", "cacheTime", "gcTimeout", "setTimeout", "optionalRemove", "updateCacheTime", "newCacheTime", "Math", "max", "isServer", "Infinity", "clearTimeout", "undefined"], "mappings": ";;AAEO,MAAeA,SAAf,CAAyB;AAI9BC,EAAAA,OAAO,GAAS;AACd,IAAA,IAAA,CAAKC,cAAL,EAAA,CAAA;AACD,GAAA;;AAESC,EAAAA,UAAU,GAAS;AAC3B,IAAA,IAAA,CAAKD,cAAL,EAAA,CAAA;;AAEA,IAAA,IAAIE,cAAc,CAAC,IAAKC,CAAAA,SAAN,CAAlB,EAAoC;AAClC,MAAA,IAAA,CAAKC,SAAL,GAAiBC,UAAU,CAAC,MAAM;AAChC,QAAA,IAAA,CAAKC,cAAL,EAAA,CAAA;OADyB,EAExB,IAAKH,CAAAA,SAFmB,CAA3B,CAAA;AAGD,KAAA;AACF,GAAA;;EAESI,eAAe,CAACC,YAAD,EAAyC;AAChE;IACA,IAAKL,CAAAA,SAAL,GAAiBM,IAAI,CAACC,GAAL,CACf,IAAA,CAAKP,SAAL,IAAkB,CADH,EAEfK,YAFe,IAEfA,IAAAA,GAAAA,YAFe,GAEEG,QAAQ,GAAGC,QAAH,GAAc,CAAI,GAAA,EAAJ,GAAS,IAFjC,CAAjB,CAAA;AAID,GAAA;;AAESZ,EAAAA,cAAc,GAAG;IACzB,IAAI,IAAA,CAAKI,SAAT,EAAoB;MAClBS,YAAY,CAAC,IAAKT,CAAAA,SAAN,CAAZ,CAAA;MACA,IAAKA,CAAAA,SAAL,GAAiBU,SAAjB,CAAA;AACD,KAAA;AACF,GAAA;;AA/B6B;;;;"}