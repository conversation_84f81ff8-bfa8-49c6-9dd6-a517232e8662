{"version": 3, "file": "queryObserver.mjs", "sources": ["../../src/queryObserver.ts"], "sourcesContent": ["import {\n  isServer,\n  isValidTimeout,\n  noop,\n  replaceData,\n  shallowEqualObjects,\n  timeUntilStale,\n} from './utils'\nimport { notifyManager } from './notifyManager'\nimport { focusManager } from './focusManager'\nimport { Subscribable } from './subscribable'\nimport { canFetch, isCancelledError } from './retryer'\nimport type {\n  PlaceholderDataFunction,\n  QueryKey,\n  QueryObserverBaseResult,\n  QueryObserverOptions,\n  QueryObserverResult,\n  QueryOptions,\n  RefetchOptions,\n} from './types'\nimport type { Action, FetchOptions, Query, QueryState } from './query'\nimport type { QueryClient } from './queryClient'\nimport type { DefaultedQueryObserverOptions, RefetchPageFilters } from './types'\n\ntype QueryObserverListener<TData, TError> = (\n  result: QueryObserverResult<TData, TError>,\n) => void\n\nexport interface NotifyOptions {\n  cache?: boolean\n  listeners?: boolean\n  onError?: boolean\n  onSuccess?: boolean\n}\n\nexport interface ObserverFetchOptions extends FetchOptions {\n  throwOnError?: boolean\n}\n\nexport class QueryObserver<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n> extends Subscribable<QueryObserverListener<TData, TError>> {\n  options: QueryObserverOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryData,\n    TQueryKey\n  >\n\n  private client: QueryClient\n  private currentQuery!: Query<TQueryFnData, TError, TQueryData, TQueryKey>\n  private currentQueryInitialState!: QueryState<TQueryData, TError>\n  private currentResult!: QueryObserverResult<TData, TError>\n  private currentResultState?: QueryState<TQueryData, TError>\n  private currentResultOptions?: QueryObserverOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryData,\n    TQueryKey\n  >\n  private previousQueryResult?: QueryObserverResult<TData, TError>\n  private selectError: TError | null\n  private selectFn?: (data: TQueryData) => TData\n  private selectResult?: TData\n  private staleTimeoutId?: ReturnType<typeof setTimeout>\n  private refetchIntervalId?: ReturnType<typeof setInterval>\n  private currentRefetchInterval?: number | false\n  private trackedProps!: Set<keyof QueryObserverResult>\n\n  constructor(\n    client: QueryClient,\n    options: QueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey\n    >,\n  ) {\n    super()\n\n    this.client = client\n    this.options = options\n    this.trackedProps = new Set()\n    this.selectError = null\n    this.bindMethods()\n    this.setOptions(options)\n  }\n\n  protected bindMethods(): void {\n    this.remove = this.remove.bind(this)\n    this.refetch = this.refetch.bind(this)\n  }\n\n  protected onSubscribe(): void {\n    if (this.listeners.size === 1) {\n      this.currentQuery.addObserver(this)\n\n      if (shouldFetchOnMount(this.currentQuery, this.options)) {\n        this.executeFetch()\n      }\n\n      this.updateTimers()\n    }\n  }\n\n  protected onUnsubscribe(): void {\n    if (!this.hasListeners()) {\n      this.destroy()\n    }\n  }\n\n  shouldFetchOnReconnect(): boolean {\n    return shouldFetchOn(\n      this.currentQuery,\n      this.options,\n      this.options.refetchOnReconnect,\n    )\n  }\n\n  shouldFetchOnWindowFocus(): boolean {\n    return shouldFetchOn(\n      this.currentQuery,\n      this.options,\n      this.options.refetchOnWindowFocus,\n    )\n  }\n\n  destroy(): void {\n    this.listeners = new Set()\n    this.clearStaleTimeout()\n    this.clearRefetchInterval()\n    this.currentQuery.removeObserver(this)\n  }\n\n  setOptions(\n    options?: QueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey\n    >,\n    notifyOptions?: NotifyOptions,\n  ): void {\n    const prevOptions = this.options\n    const prevQuery = this.currentQuery\n\n    this.options = this.client.defaultQueryOptions(options)\n\n    if (\n      process.env.NODE_ENV !== 'production' &&\n      typeof options?.isDataEqual !== 'undefined'\n    ) {\n      this.client\n        .getLogger()\n        .error(\n          `The isDataEqual option has been deprecated and will be removed in the next major version. You can achieve the same functionality by passing a function as the structuralSharing option`,\n        )\n    }\n\n    if (!shallowEqualObjects(prevOptions, this.options)) {\n      this.client.getQueryCache().notify({\n        type: 'observerOptionsUpdated',\n        query: this.currentQuery,\n        observer: this,\n      })\n    }\n\n    if (\n      typeof this.options.enabled !== 'undefined' &&\n      typeof this.options.enabled !== 'boolean'\n    ) {\n      throw new Error('Expected enabled to be a boolean')\n    }\n\n    // Keep previous query key if the user does not supply one\n    if (!this.options.queryKey) {\n      this.options.queryKey = prevOptions.queryKey\n    }\n\n    this.updateQuery()\n\n    const mounted = this.hasListeners()\n\n    // Fetch if there are subscribers\n    if (\n      mounted &&\n      shouldFetchOptionally(\n        this.currentQuery,\n        prevQuery,\n        this.options,\n        prevOptions,\n      )\n    ) {\n      this.executeFetch()\n    }\n\n    // Update result\n    this.updateResult(notifyOptions)\n\n    // Update stale interval if needed\n    if (\n      mounted &&\n      (this.currentQuery !== prevQuery ||\n        this.options.enabled !== prevOptions.enabled ||\n        this.options.staleTime !== prevOptions.staleTime)\n    ) {\n      this.updateStaleTimeout()\n    }\n\n    const nextRefetchInterval = this.computeRefetchInterval()\n\n    // Update refetch interval if needed\n    if (\n      mounted &&\n      (this.currentQuery !== prevQuery ||\n        this.options.enabled !== prevOptions.enabled ||\n        nextRefetchInterval !== this.currentRefetchInterval)\n    ) {\n      this.updateRefetchInterval(nextRefetchInterval)\n    }\n  }\n\n  getOptimisticResult(\n    options: DefaultedQueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey\n    >,\n  ): QueryObserverResult<TData, TError> {\n    const query = this.client.getQueryCache().build(this.client, options)\n\n    const result = this.createResult(query, options)\n\n    if (shouldAssignObserverCurrentProperties(this, result, options)) {\n      // this assigns the optimistic result to the current Observer\n      // because if the query function changes, useQuery will be performing\n      // an effect where it would fetch again.\n      // When the fetch finishes, we perform a deep data cloning in order\n      // to reuse objects references. This deep data clone is performed against\n      // the `observer.currentResult.data` property\n      // When QueryKey changes, we refresh the query and get new `optimistic`\n      // result, while we leave the `observer.currentResult`, so when new data\n      // arrives, it finds the old `observer.currentResult` which is related\n      // to the old QueryKey. Which means that currentResult and selectData are\n      // out of sync already.\n      // To solve this, we move the cursor of the currentResult everytime\n      // an observer reads an optimistic value.\n\n      // When keeping the previous data, the result doesn't change until new\n      // data arrives.\n      this.currentResult = result\n      this.currentResultOptions = this.options\n      this.currentResultState = this.currentQuery.state\n    }\n    return result\n  }\n\n  getCurrentResult(): QueryObserverResult<TData, TError> {\n    return this.currentResult\n  }\n\n  trackResult(\n    result: QueryObserverResult<TData, TError>,\n  ): QueryObserverResult<TData, TError> {\n    const trackedResult = {} as QueryObserverResult<TData, TError>\n\n    Object.keys(result).forEach((key) => {\n      Object.defineProperty(trackedResult, key, {\n        configurable: false,\n        enumerable: true,\n        get: () => {\n          this.trackedProps.add(key as keyof QueryObserverResult)\n          return result[key as keyof QueryObserverResult]\n        },\n      })\n    })\n\n    return trackedResult\n  }\n\n  getCurrentQuery(): Query<TQueryFnData, TError, TQueryData, TQueryKey> {\n    return this.currentQuery\n  }\n\n  remove(): void {\n    this.client.getQueryCache().remove(this.currentQuery)\n  }\n\n  refetch<TPageData>({\n    refetchPage,\n    ...options\n  }: RefetchOptions & RefetchPageFilters<TPageData> = {}): Promise<\n    QueryObserverResult<TData, TError>\n  > {\n    return this.fetch({\n      ...options,\n      meta: { refetchPage },\n    })\n  }\n\n  fetchOptimistic(\n    options: QueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey\n    >,\n  ): Promise<QueryObserverResult<TData, TError>> {\n    const defaultedOptions = this.client.defaultQueryOptions(options)\n\n    const query = this.client\n      .getQueryCache()\n      .build(this.client, defaultedOptions)\n    query.isFetchingOptimistic = true\n\n    return query.fetch().then(() => this.createResult(query, defaultedOptions))\n  }\n\n  protected fetch(\n    fetchOptions: ObserverFetchOptions,\n  ): Promise<QueryObserverResult<TData, TError>> {\n    return this.executeFetch({\n      ...fetchOptions,\n      cancelRefetch: fetchOptions.cancelRefetch ?? true,\n    }).then(() => {\n      this.updateResult()\n      return this.currentResult\n    })\n  }\n\n  private executeFetch(\n    fetchOptions?: ObserverFetchOptions,\n  ): Promise<TQueryData | undefined> {\n    // Make sure we reference the latest query as the current one might have been removed\n    this.updateQuery()\n\n    // Fetch\n    let promise: Promise<TQueryData | undefined> = this.currentQuery.fetch(\n      this.options as QueryOptions<TQueryFnData, TError, TQueryData, TQueryKey>,\n      fetchOptions,\n    )\n\n    if (!fetchOptions?.throwOnError) {\n      promise = promise.catch(noop)\n    }\n\n    return promise\n  }\n\n  private updateStaleTimeout(): void {\n    this.clearStaleTimeout()\n\n    if (\n      isServer ||\n      this.currentResult.isStale ||\n      !isValidTimeout(this.options.staleTime)\n    ) {\n      return\n    }\n\n    const time = timeUntilStale(\n      this.currentResult.dataUpdatedAt,\n      this.options.staleTime,\n    )\n\n    // The timeout is sometimes triggered 1 ms before the stale time expiration.\n    // To mitigate this issue we always add 1 ms to the timeout.\n    const timeout = time + 1\n\n    this.staleTimeoutId = setTimeout(() => {\n      if (!this.currentResult.isStale) {\n        this.updateResult()\n      }\n    }, timeout)\n  }\n\n  private computeRefetchInterval() {\n    return typeof this.options.refetchInterval === 'function'\n      ? this.options.refetchInterval(this.currentResult.data, this.currentQuery)\n      : this.options.refetchInterval ?? false\n  }\n\n  private updateRefetchInterval(nextInterval: number | false): void {\n    this.clearRefetchInterval()\n\n    this.currentRefetchInterval = nextInterval\n\n    if (\n      isServer ||\n      this.options.enabled === false ||\n      !isValidTimeout(this.currentRefetchInterval) ||\n      this.currentRefetchInterval === 0\n    ) {\n      return\n    }\n\n    this.refetchIntervalId = setInterval(() => {\n      if (\n        this.options.refetchIntervalInBackground ||\n        focusManager.isFocused()\n      ) {\n        this.executeFetch()\n      }\n    }, this.currentRefetchInterval)\n  }\n\n  private updateTimers(): void {\n    this.updateStaleTimeout()\n    this.updateRefetchInterval(this.computeRefetchInterval())\n  }\n\n  private clearStaleTimeout(): void {\n    if (this.staleTimeoutId) {\n      clearTimeout(this.staleTimeoutId)\n      this.staleTimeoutId = undefined\n    }\n  }\n\n  private clearRefetchInterval(): void {\n    if (this.refetchIntervalId) {\n      clearInterval(this.refetchIntervalId)\n      this.refetchIntervalId = undefined\n    }\n  }\n\n  protected createResult(\n    query: Query<TQueryFnData, TError, TQueryData, TQueryKey>,\n    options: QueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey\n    >,\n  ): QueryObserverResult<TData, TError> {\n    const prevQuery = this.currentQuery\n    const prevOptions = this.options\n    const prevResult = this.currentResult as\n      | QueryObserverResult<TData, TError>\n      | undefined\n    const prevResultState = this.currentResultState\n    const prevResultOptions = this.currentResultOptions\n    const queryChange = query !== prevQuery\n    const queryInitialState = queryChange\n      ? query.state\n      : this.currentQueryInitialState\n    const prevQueryResult = queryChange\n      ? this.currentResult\n      : this.previousQueryResult\n\n    const { state } = query\n    let { dataUpdatedAt, error, errorUpdatedAt, fetchStatus, status } = state\n    let isPreviousData = false\n    let isPlaceholderData = false\n    let data: TData | undefined\n\n    // Optimistically set result in fetching state if needed\n    if (options._optimisticResults) {\n      const mounted = this.hasListeners()\n\n      const fetchOnMount = !mounted && shouldFetchOnMount(query, options)\n\n      const fetchOptionally =\n        mounted && shouldFetchOptionally(query, prevQuery, options, prevOptions)\n\n      if (fetchOnMount || fetchOptionally) {\n        fetchStatus = canFetch(query.options.networkMode)\n          ? 'fetching'\n          : 'paused'\n        if (!dataUpdatedAt) {\n          status = 'loading'\n        }\n      }\n      if (options._optimisticResults === 'isRestoring') {\n        fetchStatus = 'idle'\n      }\n    }\n\n    // Keep previous data if needed\n    if (\n      options.keepPreviousData &&\n      !state.dataUpdatedAt &&\n      prevQueryResult?.isSuccess &&\n      status !== 'error'\n    ) {\n      data = prevQueryResult.data\n      dataUpdatedAt = prevQueryResult.dataUpdatedAt\n      status = prevQueryResult.status\n      isPreviousData = true\n    }\n    // Select data if needed\n    else if (options.select && typeof state.data !== 'undefined') {\n      // Memoize select result\n      if (\n        prevResult &&\n        state.data === prevResultState?.data &&\n        options.select === this.selectFn\n      ) {\n        data = this.selectResult\n      } else {\n        try {\n          this.selectFn = options.select\n          data = options.select(state.data)\n          data = replaceData(prevResult?.data, data, options)\n          this.selectResult = data\n          this.selectError = null\n        } catch (selectError) {\n          if (process.env.NODE_ENV !== 'production') {\n            this.client.getLogger().error(selectError)\n          }\n          this.selectError = selectError as TError\n        }\n      }\n    }\n    // Use query data\n    else {\n      data = state.data as unknown as TData\n    }\n\n    // Show placeholder data if needed\n    if (\n      typeof options.placeholderData !== 'undefined' &&\n      typeof data === 'undefined' &&\n      status === 'loading'\n    ) {\n      let placeholderData\n\n      // Memoize placeholder data\n      if (\n        prevResult?.isPlaceholderData &&\n        options.placeholderData === prevResultOptions?.placeholderData\n      ) {\n        placeholderData = prevResult.data\n      } else {\n        placeholderData =\n          typeof options.placeholderData === 'function'\n            ? (options.placeholderData as PlaceholderDataFunction<TQueryData>)()\n            : options.placeholderData\n        if (options.select && typeof placeholderData !== 'undefined') {\n          try {\n            placeholderData = options.select(placeholderData)\n            this.selectError = null\n          } catch (selectError) {\n            if (process.env.NODE_ENV !== 'production') {\n              this.client.getLogger().error(selectError)\n            }\n            this.selectError = selectError as TError\n          }\n        }\n      }\n\n      if (typeof placeholderData !== 'undefined') {\n        status = 'success'\n        data = replaceData(prevResult?.data, placeholderData, options) as TData\n        isPlaceholderData = true\n      }\n    }\n\n    if (this.selectError) {\n      error = this.selectError as any\n      data = this.selectResult\n      errorUpdatedAt = Date.now()\n      status = 'error'\n    }\n\n    const isFetching = fetchStatus === 'fetching'\n    const isLoading = status === 'loading'\n    const isError = status === 'error'\n\n    const result: QueryObserverBaseResult<TData, TError> = {\n      status,\n      fetchStatus,\n      isLoading,\n      isSuccess: status === 'success',\n      isError,\n      isInitialLoading: isLoading && isFetching,\n      data,\n      dataUpdatedAt,\n      error,\n      errorUpdatedAt,\n      failureCount: state.fetchFailureCount,\n      failureReason: state.fetchFailureReason,\n      errorUpdateCount: state.errorUpdateCount,\n      isFetched: state.dataUpdateCount > 0 || state.errorUpdateCount > 0,\n      isFetchedAfterMount:\n        state.dataUpdateCount > queryInitialState.dataUpdateCount ||\n        state.errorUpdateCount > queryInitialState.errorUpdateCount,\n      isFetching,\n      isRefetching: isFetching && !isLoading,\n      isLoadingError: isError && state.dataUpdatedAt === 0,\n      isPaused: fetchStatus === 'paused',\n      isPlaceholderData,\n      isPreviousData,\n      isRefetchError: isError && state.dataUpdatedAt !== 0,\n      isStale: isStale(query, options),\n      refetch: this.refetch,\n      remove: this.remove,\n    }\n\n    return result as QueryObserverResult<TData, TError>\n  }\n\n  updateResult(notifyOptions?: NotifyOptions): void {\n    const prevResult = this.currentResult as\n      | QueryObserverResult<TData, TError>\n      | undefined\n\n    const nextResult = this.createResult(this.currentQuery, this.options)\n    this.currentResultState = this.currentQuery.state\n    this.currentResultOptions = this.options\n\n    // Only notify and update result if something has changed\n    if (shallowEqualObjects(nextResult, prevResult)) {\n      return\n    }\n\n    this.currentResult = nextResult\n\n    // Determine which callbacks to trigger\n    const defaultNotifyOptions: NotifyOptions = { cache: true }\n\n    const shouldNotifyListeners = (): boolean => {\n      if (!prevResult) {\n        return true\n      }\n\n      const { notifyOnChangeProps } = this.options\n      const notifyOnChangePropsValue =\n        typeof notifyOnChangeProps === 'function'\n          ? notifyOnChangeProps()\n          : notifyOnChangeProps\n\n      if (\n        notifyOnChangePropsValue === 'all' ||\n        (!notifyOnChangePropsValue && !this.trackedProps.size)\n      ) {\n        return true\n      }\n\n      const includedProps = new Set(\n        notifyOnChangePropsValue ?? this.trackedProps,\n      )\n\n      if (this.options.useErrorBoundary) {\n        includedProps.add('error')\n      }\n\n      return Object.keys(this.currentResult).some((key) => {\n        const typedKey = key as keyof QueryObserverResult\n        const changed = this.currentResult[typedKey] !== prevResult[typedKey]\n        return changed && includedProps.has(typedKey)\n      })\n    }\n\n    if (notifyOptions?.listeners !== false && shouldNotifyListeners()) {\n      defaultNotifyOptions.listeners = true\n    }\n\n    this.notify({ ...defaultNotifyOptions, ...notifyOptions })\n  }\n\n  private updateQuery(): void {\n    const query = this.client.getQueryCache().build(this.client, this.options)\n\n    if (query === this.currentQuery) {\n      return\n    }\n\n    const prevQuery = this.currentQuery as\n      | Query<TQueryFnData, TError, TQueryData, TQueryKey>\n      | undefined\n    this.currentQuery = query\n    this.currentQueryInitialState = query.state\n    this.previousQueryResult = this.currentResult\n\n    if (this.hasListeners()) {\n      prevQuery?.removeObserver(this)\n      query.addObserver(this)\n    }\n  }\n\n  onQueryUpdate(action: Action<TData, TError>): void {\n    const notifyOptions: NotifyOptions = {}\n\n    if (action.type === 'success') {\n      notifyOptions.onSuccess = !action.manual\n    } else if (action.type === 'error' && !isCancelledError(action.error)) {\n      notifyOptions.onError = true\n    }\n\n    this.updateResult(notifyOptions)\n\n    if (this.hasListeners()) {\n      this.updateTimers()\n    }\n  }\n\n  private notify(notifyOptions: NotifyOptions): void {\n    notifyManager.batch(() => {\n      // First trigger the configuration callbacks\n      if (notifyOptions.onSuccess) {\n        this.options.onSuccess?.(this.currentResult.data!)\n        this.options.onSettled?.(this.currentResult.data!, null)\n      } else if (notifyOptions.onError) {\n        this.options.onError?.(this.currentResult.error!)\n        this.options.onSettled?.(undefined, this.currentResult.error!)\n      }\n\n      // Then trigger the listeners\n      if (notifyOptions.listeners) {\n        this.listeners.forEach(({ listener }) => {\n          listener(this.currentResult)\n        })\n      }\n\n      // Then the cache listeners\n      if (notifyOptions.cache) {\n        this.client.getQueryCache().notify({\n          query: this.currentQuery,\n          type: 'observerResultsUpdated',\n        })\n      }\n    })\n  }\n}\n\nfunction shouldLoadOnMount(\n  query: Query<any, any, any, any>,\n  options: QueryObserverOptions<any, any, any, any>,\n): boolean {\n  return (\n    options.enabled !== false &&\n    !query.state.dataUpdatedAt &&\n    !(query.state.status === 'error' && options.retryOnMount === false)\n  )\n}\n\nfunction shouldFetchOnMount(\n  query: Query<any, any, any, any>,\n  options: QueryObserverOptions<any, any, any, any, any>,\n): boolean {\n  return (\n    shouldLoadOnMount(query, options) ||\n    (query.state.dataUpdatedAt > 0 &&\n      shouldFetchOn(query, options, options.refetchOnMount))\n  )\n}\n\nfunction shouldFetchOn(\n  query: Query<any, any, any, any>,\n  options: QueryObserverOptions<any, any, any, any, any>,\n  field: typeof options['refetchOnMount'] &\n    typeof options['refetchOnWindowFocus'] &\n    typeof options['refetchOnReconnect'],\n) {\n  if (options.enabled !== false) {\n    const value = typeof field === 'function' ? field(query) : field\n\n    return value === 'always' || (value !== false && isStale(query, options))\n  }\n  return false\n}\n\nfunction shouldFetchOptionally(\n  query: Query<any, any, any, any>,\n  prevQuery: Query<any, any, any, any>,\n  options: QueryObserverOptions<any, any, any, any, any>,\n  prevOptions: QueryObserverOptions<any, any, any, any, any>,\n): boolean {\n  return (\n    options.enabled !== false &&\n    (query !== prevQuery || prevOptions.enabled === false) &&\n    (!options.suspense || query.state.status !== 'error') &&\n    isStale(query, options)\n  )\n}\n\nfunction isStale(\n  query: Query<any, any, any, any>,\n  options: QueryObserverOptions<any, any, any, any, any>,\n): boolean {\n  return query.isStaleByTime(options.staleTime)\n}\n\n// this function would decide if we will update the observer's 'current'\n// properties after an optimistic reading via getOptimisticResult\nfunction shouldAssignObserverCurrentProperties<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  observer: QueryObserver<TQueryFnData, TError, TData, TQueryData, TQueryKey>,\n  optimisticResult: QueryObserverResult<TData, TError>,\n  options: DefaultedQueryObserverOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryData,\n    TQueryKey\n  >,\n) {\n  // it is important to keep this condition like this for three reasons:\n  // 1. It will get removed in the v5\n  // 2. it reads: don't update the properties if we want to keep the previous\n  // data.\n  // 3. The opposite condition (!options.keepPreviousData) would fallthrough\n  // and will result in a bad decision\n  if (options.keepPreviousData) {\n    return false\n  }\n\n  // this means we want to put some placeholder data when pending and queryKey\n  // changed.\n  if (options.placeholderData !== undefined) {\n    // re-assign properties only if current data is placeholder data\n    // which means that data did not arrive yet, so, if there is some cached data\n    // we need to \"prepare\" to receive it\n    return optimisticResult.isPlaceholderData\n  }\n\n  // if the newly created result isn't what the observer is holding as current,\n  // then we'll need to update the properties as well\n  if (!shallowEqualObjects(observer.getCurrentResult(), optimisticResult)) {\n    return true\n  }\n\n  // basically, just keep previous properties if nothing changed\n  return false\n}\n"], "names": ["QueryObserver", "Subscribable", "constructor", "client", "options", "trackedProps", "Set", "selectError", "bindMethods", "setOptions", "remove", "bind", "refetch", "onSubscribe", "listeners", "size", "<PERSON><PERSON><PERSON><PERSON>", "addObserver", "shouldFetchOnMount", "executeFetch", "updateTimers", "onUnsubscribe", "hasListeners", "destroy", "shouldFetchOnReconnect", "shouldFetchOn", "refetchOnReconnect", "shouldFetchOnWindowFocus", "refetchOnWindowFocus", "clearStaleTimeout", "clearRefetchInterval", "removeObserver", "notifyOptions", "prevOptions", "prev<PERSON><PERSON><PERSON>", "defaultQueryOptions", "process", "env", "NODE_ENV", "isDataEqual", "<PERSON><PERSON><PERSON><PERSON>", "error", "shallowEqualObjects", "get<PERSON><PERSON><PERSON><PERSON>ache", "notify", "type", "query", "observer", "enabled", "Error", "query<PERSON><PERSON>", "updateQuery", "mounted", "shouldFetchOptionally", "updateResult", "staleTime", "updateStaleTimeout", "nextRefetchInterval", "computeRefetchInterval", "currentRefetchInterval", "updateRefetchInterval", "getOptimisticResult", "build", "result", "createResult", "shouldAssignObserverCurrentProperties", "currentResult", "currentResultOptions", "currentResultState", "state", "getCurrentResult", "trackResult", "trackedResult", "Object", "keys", "for<PERSON>ach", "key", "defineProperty", "configurable", "enumerable", "get", "add", "get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "refetchPage", "fetch", "meta", "fetchOptimistic", "defaultedOptions", "isFetchingOptimistic", "then", "fetchOptions", "cancelRefetch", "promise", "throwOnError", "catch", "noop", "isServer", "isStale", "isValidTimeout", "time", "timeUntilStale", "dataUpdatedAt", "timeout", "staleTimeoutId", "setTimeout", "refetchInterval", "data", "nextInterval", "refetchIntervalId", "setInterval", "refetchIntervalInBackground", "focusManager", "isFocused", "clearTimeout", "undefined", "clearInterval", "prevResult", "prevResultState", "prevResultOptions", "query<PERSON>hange", "queryInitialState", "currentQueryInitialState", "prevQueryResult", "previousQueryResult", "errorUpdatedAt", "fetchStatus", "status", "isPreviousData", "isPlaceholderData", "_optimisticResults", "fetchOnMount", "fetchOptionally", "canFetch", "networkMode", "keepPreviousData", "isSuccess", "select", "selectFn", "selectResult", "replaceData", "placeholderData", "Date", "now", "isFetching", "isLoading", "isError", "isInitialLoading", "failureCount", "fetchFailureCount", "failureReason", "fetchFailureReason", "errorUpdateCount", "isFetched", "dataUpdateCount", "isFetchedAfterMount", "isRefetching", "isLoadingError", "isPaused", "isRefetchError", "nextResult", "defaultNotifyOptions", "cache", "shouldNotifyListeners", "notifyOnChangeProps", "notifyOnChangePropsValue", "includedProps", "useErrorBoundary", "some", "<PERSON><PERSON><PERSON>", "changed", "has", "onQueryUpdate", "action", "onSuccess", "manual", "isCancelledError", "onError", "notify<PERSON><PERSON>ger", "batch", "onSettled", "listener", "shouldLoadOnMount", "retryOnMount", "refetchOnMount", "field", "value", "suspense", "isStaleByTime", "optimisticResult"], "mappings": ";;;;;;AAwCO,MAAMA,aAAN,SAMGC,YANH,CAMsD;AA8B3DC,EAAAA,WAAW,CACTC,MADS,EAETC,OAFS,EAST;AACA,IAAA,KAAA,EAAA,CAAA;IAEA,IAAKD,CAAAA,MAAL,GAAcA,MAAd,CAAA;IACA,IAAKC,CAAAA,OAAL,GAAeA,OAAf,CAAA;AACA,IAAA,IAAA,CAAKC,YAAL,GAAoB,IAAIC,GAAJ,EAApB,CAAA;IACA,IAAKC,CAAAA,WAAL,GAAmB,IAAnB,CAAA;AACA,IAAA,IAAA,CAAKC,WAAL,EAAA,CAAA;IACA,IAAKC,CAAAA,UAAL,CAAgBL,OAAhB,CAAA,CAAA;AACD,GAAA;;AAESI,EAAAA,WAAW,GAAS;IAC5B,IAAKE,CAAAA,MAAL,GAAc,IAAKA,CAAAA,MAAL,CAAYC,IAAZ,CAAiB,IAAjB,CAAd,CAAA;IACA,IAAKC,CAAAA,OAAL,GAAe,IAAKA,CAAAA,OAAL,CAAaD,IAAb,CAAkB,IAAlB,CAAf,CAAA;AACD,GAAA;;AAESE,EAAAA,WAAW,GAAS;AAC5B,IAAA,IAAI,KAAKC,SAAL,CAAeC,IAAf,KAAwB,CAA5B,EAA+B;AAC7B,MAAA,IAAA,CAAKC,YAAL,CAAkBC,WAAlB,CAA8B,IAA9B,CAAA,CAAA;;MAEA,IAAIC,kBAAkB,CAAC,IAAKF,CAAAA,YAAN,EAAoB,IAAKZ,CAAAA,OAAzB,CAAtB,EAAyD;AACvD,QAAA,IAAA,CAAKe,YAAL,EAAA,CAAA;AACD,OAAA;;AAED,MAAA,IAAA,CAAKC,YAAL,EAAA,CAAA;AACD,KAAA;AACF,GAAA;;AAESC,EAAAA,aAAa,GAAS;AAC9B,IAAA,IAAI,CAAC,IAAA,CAAKC,YAAL,EAAL,EAA0B;AACxB,MAAA,IAAA,CAAKC,OAAL,EAAA,CAAA;AACD,KAAA;AACF,GAAA;;AAEDC,EAAAA,sBAAsB,GAAY;AAChC,IAAA,OAAOC,aAAa,CAClB,IAAKT,CAAAA,YADa,EAElB,IAAA,CAAKZ,OAFa,EAGlB,IAAKA,CAAAA,OAAL,CAAasB,kBAHK,CAApB,CAAA;AAKD,GAAA;;AAEDC,EAAAA,wBAAwB,GAAY;AAClC,IAAA,OAAOF,aAAa,CAClB,IAAKT,CAAAA,YADa,EAElB,IAAA,CAAKZ,OAFa,EAGlB,IAAKA,CAAAA,OAAL,CAAawB,oBAHK,CAApB,CAAA;AAKD,GAAA;;AAEDL,EAAAA,OAAO,GAAS;AACd,IAAA,IAAA,CAAKT,SAAL,GAAiB,IAAIR,GAAJ,EAAjB,CAAA;AACA,IAAA,IAAA,CAAKuB,iBAAL,EAAA,CAAA;AACA,IAAA,IAAA,CAAKC,oBAAL,EAAA,CAAA;AACA,IAAA,IAAA,CAAKd,YAAL,CAAkBe,cAAlB,CAAiC,IAAjC,CAAA,CAAA;AACD,GAAA;;AAEDtB,EAAAA,UAAU,CACRL,OADQ,EAQR4B,aARQ,EASF;IACN,MAAMC,WAAW,GAAG,IAAA,CAAK7B,OAAzB,CAAA;IACA,MAAM8B,SAAS,GAAG,IAAA,CAAKlB,YAAvB,CAAA;IAEA,IAAKZ,CAAAA,OAAL,GAAe,IAAKD,CAAAA,MAAL,CAAYgC,mBAAZ,CAAgC/B,OAAhC,CAAf,CAAA;;AAEA,IAAA,IACEgC,OAAO,CAACC,GAAR,CAAYC,QAAZ,KAAyB,YAAzB,IACA,QAAOlC,OAAP,oBAAOA,OAAO,CAAEmC,WAAhB,CAAA,KAAgC,WAFlC,EAGE;AACA,MAAA,IAAA,CAAKpC,MAAL,CACGqC,SADH,EAAA,CAEGC,KAFH,CAAA,wLAAA,CAAA,CAAA;AAKD,KAAA;;IAED,IAAI,CAACC,mBAAmB,CAACT,WAAD,EAAc,IAAK7B,CAAAA,OAAnB,CAAxB,EAAqD;AACnD,MAAA,IAAA,CAAKD,MAAL,CAAYwC,aAAZ,EAAA,CAA4BC,MAA5B,CAAmC;AACjCC,QAAAA,IAAI,EAAE,wBAD2B;QAEjCC,KAAK,EAAE,KAAK9B,YAFqB;AAGjC+B,QAAAA,QAAQ,EAAE,IAAA;OAHZ,CAAA,CAAA;AAKD,KAAA;;AAED,IAAA,IACE,OAAO,IAAA,CAAK3C,OAAL,CAAa4C,OAApB,KAAgC,WAAhC,IACA,OAAO,KAAK5C,OAAL,CAAa4C,OAApB,KAAgC,SAFlC,EAGE;AACA,MAAA,MAAM,IAAIC,KAAJ,CAAU,kCAAV,CAAN,CAAA;AACD,KA9BK;;;AAiCN,IAAA,IAAI,CAAC,IAAA,CAAK7C,OAAL,CAAa8C,QAAlB,EAA4B;AAC1B,MAAA,IAAA,CAAK9C,OAAL,CAAa8C,QAAb,GAAwBjB,WAAW,CAACiB,QAApC,CAAA;AACD,KAAA;;AAED,IAAA,IAAA,CAAKC,WAAL,EAAA,CAAA;AAEA,IAAA,MAAMC,OAAO,GAAG,IAAA,CAAK9B,YAAL,EAAhB,CAvCM;;AA0CN,IAAA,IACE8B,OAAO,IACPC,qBAAqB,CACnB,KAAKrC,YADc,EAEnBkB,SAFmB,EAGnB,IAAK9B,CAAAA,OAHc,EAInB6B,WAJmB,CAFvB,EAQE;AACA,MAAA,IAAA,CAAKd,YAAL,EAAA,CAAA;AACD,KApDK;;;AAuDN,IAAA,IAAA,CAAKmC,YAAL,CAAkBtB,aAAlB,CAAA,CAvDM;;IA0DN,IACEoB,OAAO,KACN,IAAA,CAAKpC,YAAL,KAAsBkB,SAAtB,IACC,IAAA,CAAK9B,OAAL,CAAa4C,OAAb,KAAyBf,WAAW,CAACe,OADtC,IAEC,IAAA,CAAK5C,OAAL,CAAamD,SAAb,KAA2BtB,WAAW,CAACsB,SAHlC,CADT,EAKE;AACA,MAAA,IAAA,CAAKC,kBAAL,EAAA,CAAA;AACD,KAAA;;AAED,IAAA,MAAMC,mBAAmB,GAAG,IAAA,CAAKC,sBAAL,EAA5B,CAnEM;;IAsEN,IACEN,OAAO,KACN,IAAKpC,CAAAA,YAAL,KAAsBkB,SAAtB,IACC,KAAK9B,OAAL,CAAa4C,OAAb,KAAyBf,WAAW,CAACe,OADtC,IAECS,mBAAmB,KAAK,IAAA,CAAKE,sBAHxB,CADT,EAKE;MACA,IAAKC,CAAAA,qBAAL,CAA2BH,mBAA3B,CAAA,CAAA;AACD,KAAA;AACF,GAAA;;EAEDI,mBAAmB,CACjBzD,OADiB,EAQmB;AACpC,IAAA,MAAM0C,KAAK,GAAG,IAAK3C,CAAAA,MAAL,CAAYwC,aAAZ,EAA4BmB,CAAAA,KAA5B,CAAkC,IAAA,CAAK3D,MAAvC,EAA+CC,OAA/C,CAAd,CAAA;IAEA,MAAM2D,MAAM,GAAG,IAAKC,CAAAA,YAAL,CAAkBlB,KAAlB,EAAyB1C,OAAzB,CAAf,CAAA;;IAEA,IAAI6D,qCAAqC,CAAC,IAAD,EAAOF,MAAP,EAAe3D,OAAf,CAAzC,EAAkE;AAChE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;MACA,IAAK8D,CAAAA,aAAL,GAAqBH,MAArB,CAAA;MACA,IAAKI,CAAAA,oBAAL,GAA4B,IAAA,CAAK/D,OAAjC,CAAA;AACA,MAAA,IAAA,CAAKgE,kBAAL,GAA0B,IAAKpD,CAAAA,YAAL,CAAkBqD,KAA5C,CAAA;AACD,KAAA;;AACD,IAAA,OAAON,MAAP,CAAA;AACD,GAAA;;AAEDO,EAAAA,gBAAgB,GAAuC;AACrD,IAAA,OAAO,KAAKJ,aAAZ,CAAA;AACD,GAAA;;EAEDK,WAAW,CACTR,MADS,EAE2B;IACpC,MAAMS,aAAa,GAAG,EAAtB,CAAA;IAEAC,MAAM,CAACC,IAAP,CAAYX,MAAZ,EAAoBY,OAApB,CAA6BC,GAAD,IAAS;AACnCH,MAAAA,MAAM,CAACI,cAAP,CAAsBL,aAAtB,EAAqCI,GAArC,EAA0C;AACxCE,QAAAA,YAAY,EAAE,KAD0B;AAExCC,QAAAA,UAAU,EAAE,IAF4B;AAGxCC,QAAAA,GAAG,EAAE,MAAM;AACT,UAAA,IAAA,CAAK3E,YAAL,CAAkB4E,GAAlB,CAAsBL,GAAtB,CAAA,CAAA;UACA,OAAOb,MAAM,CAACa,GAAD,CAAb,CAAA;AACD,SAAA;OANH,CAAA,CAAA;KADF,CAAA,CAAA;AAWA,IAAA,OAAOJ,aAAP,CAAA;AACD,GAAA;;AAEDU,EAAAA,eAAe,GAAuD;AACpE,IAAA,OAAO,KAAKlE,YAAZ,CAAA;AACD,GAAA;;AAEDN,EAAAA,MAAM,GAAS;AACb,IAAA,IAAA,CAAKP,MAAL,CAAYwC,aAAZ,GAA4BjC,MAA5B,CAAmC,KAAKM,YAAxC,CAAA,CAAA;AACD,GAAA;;AAEDJ,EAAAA,OAAO,CAAY;IACjBuE,WADiB;IAEjB,GAAG/E,OAAAA;AAFc,GAAA,GAGiC,EAH7C,EAKL;AACA,IAAA,OAAO,IAAKgF,CAAAA,KAAL,CAAW,EAChB,GAAGhF,OADa;AAEhBiF,MAAAA,IAAI,EAAE;AAAEF,QAAAA,WAAAA;AAAF,OAAA;AAFU,KAAX,CAAP,CAAA;AAID,GAAA;;EAEDG,eAAe,CACblF,OADa,EAQgC;IAC7C,MAAMmF,gBAAgB,GAAG,IAAKpF,CAAAA,MAAL,CAAYgC,mBAAZ,CAAgC/B,OAAhC,CAAzB,CAAA;AAEA,IAAA,MAAM0C,KAAK,GAAG,IAAK3C,CAAAA,MAAL,CACXwC,aADW,EAEXmB,CAAAA,KAFW,CAEL,IAAA,CAAK3D,MAFA,EAEQoF,gBAFR,CAAd,CAAA;IAGAzC,KAAK,CAAC0C,oBAAN,GAA6B,IAA7B,CAAA;AAEA,IAAA,OAAO1C,KAAK,CAACsC,KAAN,EAAA,CAAcK,IAAd,CAAmB,MAAM,IAAKzB,CAAAA,YAAL,CAAkBlB,KAAlB,EAAyByC,gBAAzB,CAAzB,CAAP,CAAA;AACD,GAAA;;EAESH,KAAK,CACbM,YADa,EAEgC;AAAA,IAAA,IAAA,qBAAA,CAAA;;AAC7C,IAAA,OAAO,IAAKvE,CAAAA,YAAL,CAAkB,EACvB,GAAGuE,YADoB;AAEvBC,MAAAA,aAAa,EAAED,CAAAA,qBAAAA,GAAAA,YAAY,CAACC,aAAf,KAAgC,IAAA,GAAA,qBAAA,GAAA,IAAA;KAFxC,CAAA,CAGJF,IAHI,CAGC,MAAM;AACZ,MAAA,IAAA,CAAKnC,YAAL,EAAA,CAAA;AACA,MAAA,OAAO,KAAKY,aAAZ,CAAA;AACD,KANM,CAAP,CAAA;AAOD,GAAA;;EAEO/C,YAAY,CAClBuE,YADkB,EAEe;AACjC;IACA,IAAKvC,CAAAA,WAAL,GAFiC;;IAKjC,IAAIyC,OAAwC,GAAG,IAAA,CAAK5E,YAAL,CAAkBoE,KAAlB,CAC7C,IAAKhF,CAAAA,OADwC,EAE7CsF,YAF6C,CAA/C,CAAA;;AAKA,IAAA,IAAI,EAACA,YAAD,IAAA,IAAA,IAACA,YAAY,CAAEG,YAAf,CAAJ,EAAiC;AAC/BD,MAAAA,OAAO,GAAGA,OAAO,CAACE,KAAR,CAAcC,IAAd,CAAV,CAAA;AACD,KAAA;;AAED,IAAA,OAAOH,OAAP,CAAA;AACD,GAAA;;AAEOpC,EAAAA,kBAAkB,GAAS;AACjC,IAAA,IAAA,CAAK3B,iBAAL,EAAA,CAAA;;AAEA,IAAA,IACEmE,QAAQ,IACR,IAAK9B,CAAAA,aAAL,CAAmB+B,OADnB,IAEA,CAACC,cAAc,CAAC,IAAK9F,CAAAA,OAAL,CAAamD,SAAd,CAHjB,EAIE;AACA,MAAA,OAAA;AACD,KAAA;;AAED,IAAA,MAAM4C,IAAI,GAAGC,cAAc,CACzB,KAAKlC,aAAL,CAAmBmC,aADM,EAEzB,KAAKjG,OAAL,CAAamD,SAFY,CAA3B,CAXiC;AAiBjC;;AACA,IAAA,MAAM+C,OAAO,GAAGH,IAAI,GAAG,CAAvB,CAAA;AAEA,IAAA,IAAA,CAAKI,cAAL,GAAsBC,UAAU,CAAC,MAAM;AACrC,MAAA,IAAI,CAAC,IAAA,CAAKtC,aAAL,CAAmB+B,OAAxB,EAAiC;AAC/B,QAAA,IAAA,CAAK3C,YAAL,EAAA,CAAA;AACD,OAAA;KAH6B,EAI7BgD,OAJ6B,CAAhC,CAAA;AAKD,GAAA;;AAEO5C,EAAAA,sBAAsB,GAAG;AAAA,IAAA,IAAA,qBAAA,CAAA;;IAC/B,OAAO,OAAO,IAAKtD,CAAAA,OAAL,CAAaqG,eAApB,KAAwC,UAAxC,GACH,IAAA,CAAKrG,OAAL,CAAaqG,eAAb,CAA6B,IAAKvC,CAAAA,aAAL,CAAmBwC,IAAhD,EAAsD,IAAA,CAAK1F,YAA3D,CADG,GAEH,CAAA,qBAAA,GAAA,IAAA,CAAKZ,OAAL,CAAaqG,eAFV,KAAA,IAAA,GAAA,qBAAA,GAE6B,KAFpC,CAAA;AAGD,GAAA;;EAEO7C,qBAAqB,CAAC+C,YAAD,EAAqC;AAChE,IAAA,IAAA,CAAK7E,oBAAL,EAAA,CAAA;IAEA,IAAK6B,CAAAA,sBAAL,GAA8BgD,YAA9B,CAAA;;IAEA,IACEX,QAAQ,IACR,IAAK5F,CAAAA,OAAL,CAAa4C,OAAb,KAAyB,KADzB,IAEA,CAACkD,cAAc,CAAC,IAAA,CAAKvC,sBAAN,CAFf,IAGA,KAAKA,sBAAL,KAAgC,CAJlC,EAKE;AACA,MAAA,OAAA;AACD,KAAA;;AAED,IAAA,IAAA,CAAKiD,iBAAL,GAAyBC,WAAW,CAAC,MAAM;MACzC,IACE,IAAA,CAAKzG,OAAL,CAAa0G,2BAAb,IACAC,YAAY,CAACC,SAAb,EAFF,EAGE;AACA,QAAA,IAAA,CAAK7F,YAAL,EAAA,CAAA;AACD,OAAA;KANiC,EAOjC,IAAKwC,CAAAA,sBAP4B,CAApC,CAAA;AAQD,GAAA;;AAEOvC,EAAAA,YAAY,GAAS;AAC3B,IAAA,IAAA,CAAKoC,kBAAL,EAAA,CAAA;AACA,IAAA,IAAA,CAAKI,qBAAL,CAA2B,IAAKF,CAAAA,sBAAL,EAA3B,CAAA,CAAA;AACD,GAAA;;AAEO7B,EAAAA,iBAAiB,GAAS;IAChC,IAAI,IAAA,CAAK0E,cAAT,EAAyB;MACvBU,YAAY,CAAC,IAAKV,CAAAA,cAAN,CAAZ,CAAA;MACA,IAAKA,CAAAA,cAAL,GAAsBW,SAAtB,CAAA;AACD,KAAA;AACF,GAAA;;AAEOpF,EAAAA,oBAAoB,GAAS;IACnC,IAAI,IAAA,CAAK8E,iBAAT,EAA4B;MAC1BO,aAAa,CAAC,IAAKP,CAAAA,iBAAN,CAAb,CAAA;MACA,IAAKA,CAAAA,iBAAL,GAAyBM,SAAzB,CAAA;AACD,KAAA;AACF,GAAA;;AAESlD,EAAAA,YAAY,CACpBlB,KADoB,EAEpB1C,OAFoB,EASgB;IACpC,MAAM8B,SAAS,GAAG,IAAA,CAAKlB,YAAvB,CAAA;IACA,MAAMiB,WAAW,GAAG,IAAA,CAAK7B,OAAzB,CAAA;IACA,MAAMgH,UAAU,GAAG,IAAA,CAAKlD,aAAxB,CAAA;IAGA,MAAMmD,eAAe,GAAG,IAAA,CAAKjD,kBAA7B,CAAA;IACA,MAAMkD,iBAAiB,GAAG,IAAA,CAAKnD,oBAA/B,CAAA;AACA,IAAA,MAAMoD,WAAW,GAAGzE,KAAK,KAAKZ,SAA9B,CAAA;IACA,MAAMsF,iBAAiB,GAAGD,WAAW,GACjCzE,KAAK,CAACuB,KAD2B,GAEjC,IAAA,CAAKoD,wBAFT,CAAA;IAGA,MAAMC,eAAe,GAAGH,WAAW,GAC/B,KAAKrD,aAD0B,GAE/B,KAAKyD,mBAFT,CAAA;IAIA,MAAM;AAAEtD,MAAAA,KAAAA;AAAF,KAAA,GAAYvB,KAAlB,CAAA;IACA,IAAI;MAAEuD,aAAF;MAAiB5D,KAAjB;MAAwBmF,cAAxB;MAAwCC,WAAxC;AAAqDC,MAAAA,MAAAA;AAArD,KAAA,GAAgEzD,KAApE,CAAA;IACA,IAAI0D,cAAc,GAAG,KAArB,CAAA;IACA,IAAIC,iBAAiB,GAAG,KAAxB,CAAA;IACA,IAAItB,IAAJ,CApBoC;;IAuBpC,IAAItG,OAAO,CAAC6H,kBAAZ,EAAgC;AAC9B,MAAA,MAAM7E,OAAO,GAAG,IAAK9B,CAAAA,YAAL,EAAhB,CAAA;MAEA,MAAM4G,YAAY,GAAG,CAAC9E,OAAD,IAAYlC,kBAAkB,CAAC4B,KAAD,EAAQ1C,OAAR,CAAnD,CAAA;AAEA,MAAA,MAAM+H,eAAe,GACnB/E,OAAO,IAAIC,qBAAqB,CAACP,KAAD,EAAQZ,SAAR,EAAmB9B,OAAnB,EAA4B6B,WAA5B,CADlC,CAAA;;MAGA,IAAIiG,YAAY,IAAIC,eAApB,EAAqC;AACnCN,QAAAA,WAAW,GAAGO,QAAQ,CAACtF,KAAK,CAAC1C,OAAN,CAAciI,WAAf,CAAR,GACV,UADU,GAEV,QAFJ,CAAA;;QAGA,IAAI,CAAChC,aAAL,EAAoB;AAClByB,UAAAA,MAAM,GAAG,SAAT,CAAA;AACD,SAAA;AACF,OAAA;;AACD,MAAA,IAAI1H,OAAO,CAAC6H,kBAAR,KAA+B,aAAnC,EAAkD;AAChDJ,QAAAA,WAAW,GAAG,MAAd,CAAA;AACD,OAAA;AACF,KA1CmC;;;AA6CpC,IAAA,IACEzH,OAAO,CAACkI,gBAAR,IACA,CAACjE,KAAK,CAACgC,aADP,IAEAqB,eAFA,IAAA,IAAA,IAEAA,eAAe,CAAEa,SAFjB,IAGAT,MAAM,KAAK,OAJb,EAKE;MACApB,IAAI,GAAGgB,eAAe,CAAChB,IAAvB,CAAA;MACAL,aAAa,GAAGqB,eAAe,CAACrB,aAAhC,CAAA;MACAyB,MAAM,GAAGJ,eAAe,CAACI,MAAzB,CAAA;AACAC,MAAAA,cAAc,GAAG,IAAjB,CAAA;AACD,KAVD;SAYK,IAAI3H,OAAO,CAACoI,MAAR,IAAkB,OAAOnE,KAAK,CAACqC,IAAb,KAAsB,WAA5C,EAAyD;AAC5D;AACA,MAAA,IACEU,UAAU,IACV/C,KAAK,CAACqC,IAAN,MAAeW,eAAf,IAAeA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,eAAe,CAAEX,IAAhC,CADA,IAEAtG,OAAO,CAACoI,MAAR,KAAmB,IAAA,CAAKC,QAH1B,EAIE;QACA/B,IAAI,GAAG,KAAKgC,YAAZ,CAAA;AACD,OAND,MAMO;QACL,IAAI;AACF,UAAA,IAAA,CAAKD,QAAL,GAAgBrI,OAAO,CAACoI,MAAxB,CAAA;UACA9B,IAAI,GAAGtG,OAAO,CAACoI,MAAR,CAAenE,KAAK,CAACqC,IAArB,CAAP,CAAA;AACAA,UAAAA,IAAI,GAAGiC,WAAW,CAACvB,UAAD,IAACA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,UAAU,CAAEV,IAAb,EAAmBA,IAAnB,EAAyBtG,OAAzB,CAAlB,CAAA;UACA,IAAKsI,CAAAA,YAAL,GAAoBhC,IAApB,CAAA;UACA,IAAKnG,CAAAA,WAAL,GAAmB,IAAnB,CAAA;SALF,CAME,OAAOA,WAAP,EAAoB;AACpB,UAAA,IAAI6B,OAAO,CAACC,GAAR,CAAYC,QAAZ,KAAyB,YAA7B,EAA2C;AACzC,YAAA,IAAA,CAAKnC,MAAL,CAAYqC,SAAZ,EAAwBC,CAAAA,KAAxB,CAA8BlC,WAA9B,CAAA,CAAA;AACD,WAAA;;UACD,IAAKA,CAAAA,WAAL,GAAmBA,WAAnB,CAAA;AACD,SAAA;AACF,OAAA;AACF,KAtBI;SAwBA;MACHmG,IAAI,GAAGrC,KAAK,CAACqC,IAAb,CAAA;AACD,KAnFmC;;;AAsFpC,IAAA,IACE,OAAOtG,OAAO,CAACwI,eAAf,KAAmC,WAAnC,IACA,OAAOlC,IAAP,KAAgB,WADhB,IAEAoB,MAAM,KAAK,SAHb,EAIE;MACA,IAAIc,eAAJ,CADA;;AAIA,MAAA,IACExB,UAAU,IAAV,IAAA,IAAAA,UAAU,CAAEY,iBAAZ,IACA5H,OAAO,CAACwI,eAAR,MAA4BtB,iBAA5B,IAAA,IAAA,GAAA,KAAA,CAAA,GAA4BA,iBAAiB,CAAEsB,eAA/C,CAFF,EAGE;QACAA,eAAe,GAAGxB,UAAU,CAACV,IAA7B,CAAA;AACD,OALD,MAKO;AACLkC,QAAAA,eAAe,GACb,OAAOxI,OAAO,CAACwI,eAAf,KAAmC,UAAnC,GACKxI,OAAO,CAACwI,eAAT,EADJ,GAEIxI,OAAO,CAACwI,eAHd,CAAA;;QAIA,IAAIxI,OAAO,CAACoI,MAAR,IAAkB,OAAOI,eAAP,KAA2B,WAAjD,EAA8D;UAC5D,IAAI;AACFA,YAAAA,eAAe,GAAGxI,OAAO,CAACoI,MAAR,CAAeI,eAAf,CAAlB,CAAA;YACA,IAAKrI,CAAAA,WAAL,GAAmB,IAAnB,CAAA;WAFF,CAGE,OAAOA,WAAP,EAAoB;AACpB,YAAA,IAAI6B,OAAO,CAACC,GAAR,CAAYC,QAAZ,KAAyB,YAA7B,EAA2C;AACzC,cAAA,IAAA,CAAKnC,MAAL,CAAYqC,SAAZ,EAAwBC,CAAAA,KAAxB,CAA8BlC,WAA9B,CAAA,CAAA;AACD,aAAA;;YACD,IAAKA,CAAAA,WAAL,GAAmBA,WAAnB,CAAA;AACD,WAAA;AACF,SAAA;AACF,OAAA;;AAED,MAAA,IAAI,OAAOqI,eAAP,KAA2B,WAA/B,EAA4C;AAC1Cd,QAAAA,MAAM,GAAG,SAAT,CAAA;AACApB,QAAAA,IAAI,GAAGiC,WAAW,CAACvB,UAAD,IAACA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,UAAU,CAAEV,IAAb,EAAmBkC,eAAnB,EAAoCxI,OAApC,CAAlB,CAAA;AACA4H,QAAAA,iBAAiB,GAAG,IAApB,CAAA;AACD,OAAA;AACF,KAAA;;IAED,IAAI,IAAA,CAAKzH,WAAT,EAAsB;MACpBkC,KAAK,GAAG,KAAKlC,WAAb,CAAA;MACAmG,IAAI,GAAG,KAAKgC,YAAZ,CAAA;AACAd,MAAAA,cAAc,GAAGiB,IAAI,CAACC,GAAL,EAAjB,CAAA;AACAhB,MAAAA,MAAM,GAAG,OAAT,CAAA;AACD,KAAA;;AAED,IAAA,MAAMiB,UAAU,GAAGlB,WAAW,KAAK,UAAnC,CAAA;AACA,IAAA,MAAMmB,SAAS,GAAGlB,MAAM,KAAK,SAA7B,CAAA;AACA,IAAA,MAAMmB,OAAO,GAAGnB,MAAM,KAAK,OAA3B,CAAA;AAEA,IAAA,MAAM/D,MAA8C,GAAG;MACrD+D,MADqD;MAErDD,WAFqD;MAGrDmB,SAHqD;MAIrDT,SAAS,EAAET,MAAM,KAAK,SAJ+B;MAKrDmB,OALqD;MAMrDC,gBAAgB,EAAEF,SAAS,IAAID,UANsB;MAOrDrC,IAPqD;MAQrDL,aARqD;MASrD5D,KATqD;MAUrDmF,cAVqD;MAWrDuB,YAAY,EAAE9E,KAAK,CAAC+E,iBAXiC;MAYrDC,aAAa,EAAEhF,KAAK,CAACiF,kBAZgC;MAarDC,gBAAgB,EAAElF,KAAK,CAACkF,gBAb6B;MAcrDC,SAAS,EAAEnF,KAAK,CAACoF,eAAN,GAAwB,CAAxB,IAA6BpF,KAAK,CAACkF,gBAAN,GAAyB,CAdZ;AAerDG,MAAAA,mBAAmB,EACjBrF,KAAK,CAACoF,eAAN,GAAwBjC,iBAAiB,CAACiC,eAA1C,IACApF,KAAK,CAACkF,gBAAN,GAAyB/B,iBAAiB,CAAC+B,gBAjBQ;MAkBrDR,UAlBqD;AAmBrDY,MAAAA,YAAY,EAAEZ,UAAU,IAAI,CAACC,SAnBwB;AAoBrDY,MAAAA,cAAc,EAAEX,OAAO,IAAI5E,KAAK,CAACgC,aAAN,KAAwB,CApBE;MAqBrDwD,QAAQ,EAAEhC,WAAW,KAAK,QArB2B;MAsBrDG,iBAtBqD;MAuBrDD,cAvBqD;AAwBrD+B,MAAAA,cAAc,EAAEb,OAAO,IAAI5E,KAAK,CAACgC,aAAN,KAAwB,CAxBE;AAyBrDJ,MAAAA,OAAO,EAAEA,OAAO,CAACnD,KAAD,EAAQ1C,OAAR,CAzBqC;MA0BrDQ,OAAO,EAAE,KAAKA,OA1BuC;AA2BrDF,MAAAA,MAAM,EAAE,IAAKA,CAAAA,MAAAA;KA3Bf,CAAA;AA8BA,IAAA,OAAOqD,MAAP,CAAA;AACD,GAAA;;EAEDT,YAAY,CAACtB,aAAD,EAAsC;IAChD,MAAMoF,UAAU,GAAG,IAAA,CAAKlD,aAAxB,CAAA;IAIA,MAAM6F,UAAU,GAAG,IAAA,CAAK/F,YAAL,CAAkB,KAAKhD,YAAvB,EAAqC,IAAKZ,CAAAA,OAA1C,CAAnB,CAAA;AACA,IAAA,IAAA,CAAKgE,kBAAL,GAA0B,IAAKpD,CAAAA,YAAL,CAAkBqD,KAA5C,CAAA;AACA,IAAA,IAAA,CAAKF,oBAAL,GAA4B,IAAK/D,CAAAA,OAAjC,CAPgD;;AAUhD,IAAA,IAAIsC,mBAAmB,CAACqH,UAAD,EAAa3C,UAAb,CAAvB,EAAiD;AAC/C,MAAA,OAAA;AACD,KAAA;;AAED,IAAA,IAAA,CAAKlD,aAAL,GAAqB6F,UAArB,CAdgD;;AAiBhD,IAAA,MAAMC,oBAAmC,GAAG;AAAEC,MAAAA,KAAK,EAAE,IAAA;KAArD,CAAA;;IAEA,MAAMC,qBAAqB,GAAG,MAAe;MAC3C,IAAI,CAAC9C,UAAL,EAAiB;AACf,QAAA,OAAO,IAAP,CAAA;AACD,OAAA;;MAED,MAAM;AAAE+C,QAAAA,mBAAAA;AAAF,OAAA,GAA0B,KAAK/J,OAArC,CAAA;MACA,MAAMgK,wBAAwB,GAC5B,OAAOD,mBAAP,KAA+B,UAA/B,GACIA,mBAAmB,EADvB,GAEIA,mBAHN,CAAA;;AAKA,MAAA,IACEC,wBAAwB,KAAK,KAA7B,IACC,CAACA,wBAAD,IAA6B,CAAC,IAAK/J,CAAAA,YAAL,CAAkBU,IAFnD,EAGE;AACA,QAAA,OAAO,IAAP,CAAA;AACD,OAAA;;MAED,MAAMsJ,aAAa,GAAG,IAAI/J,GAAJ,CACpB8J,wBADoB,IAAA,IAAA,GACpBA,wBADoB,GACQ,IAAK/J,CAAAA,YADb,CAAtB,CAAA;;AAIA,MAAA,IAAI,IAAKD,CAAAA,OAAL,CAAakK,gBAAjB,EAAmC;QACjCD,aAAa,CAACpF,GAAd,CAAkB,OAAlB,CAAA,CAAA;AACD,OAAA;;MAED,OAAOR,MAAM,CAACC,IAAP,CAAY,IAAA,CAAKR,aAAjB,CAAgCqG,CAAAA,IAAhC,CAAsC3F,GAAD,IAAS;QACnD,MAAM4F,QAAQ,GAAG5F,GAAjB,CAAA;QACA,MAAM6F,OAAO,GAAG,IAAA,CAAKvG,aAAL,CAAmBsG,QAAnB,CAAiCpD,KAAAA,UAAU,CAACoD,QAAD,CAA3D,CAAA;AACA,QAAA,OAAOC,OAAO,IAAIJ,aAAa,CAACK,GAAd,CAAkBF,QAAlB,CAAlB,CAAA;AACD,OAJM,CAAP,CAAA;KA1BF,CAAA;;AAiCA,IAAA,IAAI,CAAAxI,aAAa,IAAb,IAAA,GAAA,KAAA,CAAA,GAAAA,aAAa,CAAElB,SAAf,MAA6B,KAA7B,IAAsCoJ,qBAAqB,EAA/D,EAAmE;MACjEF,oBAAoB,CAAClJ,SAArB,GAAiC,IAAjC,CAAA;AACD,KAAA;;AAED,IAAA,IAAA,CAAK8B,MAAL,CAAY,EAAE,GAAGoH,oBAAL;MAA2B,GAAGhI,aAAAA;KAA1C,CAAA,CAAA;AACD,GAAA;;AAEOmB,EAAAA,WAAW,GAAS;AAC1B,IAAA,MAAML,KAAK,GAAG,IAAK3C,CAAAA,MAAL,CAAYwC,aAAZ,EAAA,CAA4BmB,KAA5B,CAAkC,IAAK3D,CAAAA,MAAvC,EAA+C,IAAA,CAAKC,OAApD,CAAd,CAAA;;AAEA,IAAA,IAAI0C,KAAK,KAAK,IAAK9B,CAAAA,YAAnB,EAAiC;AAC/B,MAAA,OAAA;AACD,KAAA;;IAED,MAAMkB,SAAS,GAAG,IAAA,CAAKlB,YAAvB,CAAA;IAGA,IAAKA,CAAAA,YAAL,GAAoB8B,KAApB,CAAA;AACA,IAAA,IAAA,CAAK2E,wBAAL,GAAgC3E,KAAK,CAACuB,KAAtC,CAAA;IACA,IAAKsD,CAAAA,mBAAL,GAA2B,IAAA,CAAKzD,aAAhC,CAAA;;IAEA,IAAI,IAAA,CAAK5C,YAAL,EAAJ,EAAyB;AACvBY,MAAAA,SAAS,QAAT,GAAAA,KAAAA,CAAAA,GAAAA,SAAS,CAAEH,cAAX,CAA0B,IAA1B,CAAA,CAAA;MACAe,KAAK,CAAC7B,WAAN,CAAkB,IAAlB,CAAA,CAAA;AACD,KAAA;AACF,GAAA;;EAED0J,aAAa,CAACC,MAAD,EAAsC;IACjD,MAAM5I,aAA4B,GAAG,EAArC,CAAA;;AAEA,IAAA,IAAI4I,MAAM,CAAC/H,IAAP,KAAgB,SAApB,EAA+B;AAC7Bb,MAAAA,aAAa,CAAC6I,SAAd,GAA0B,CAACD,MAAM,CAACE,MAAlC,CAAA;AACD,KAFD,MAEO,IAAIF,MAAM,CAAC/H,IAAP,KAAgB,OAAhB,IAA2B,CAACkI,gBAAgB,CAACH,MAAM,CAACnI,KAAR,CAAhD,EAAgE;MACrET,aAAa,CAACgJ,OAAd,GAAwB,IAAxB,CAAA;AACD,KAAA;;IAED,IAAK1H,CAAAA,YAAL,CAAkBtB,aAAlB,CAAA,CAAA;;IAEA,IAAI,IAAA,CAAKV,YAAL,EAAJ,EAAyB;AACvB,MAAA,IAAA,CAAKF,YAAL,EAAA,CAAA;AACD,KAAA;AACF,GAAA;;EAEOwB,MAAM,CAACZ,aAAD,EAAqC;IACjDiJ,aAAa,CAACC,KAAd,CAAoB,MAAM;AACxB;MACA,IAAIlJ,aAAa,CAAC6I,SAAlB,EAA6B;AAAA,QAAA,IAAA,qBAAA,EAAA,aAAA,EAAA,qBAAA,EAAA,cAAA,CAAA;;AAC3B,QAAA,CAAA,qBAAA,GAAA,CAAA,aAAA,GAAA,IAAA,CAAKzK,OAAL,EAAayK,SAAb,+DAAyB,IAAK3G,CAAAA,aAAL,CAAmBwC,IAA5C,CAAA,CAAA;QACA,CAAKtG,qBAAAA,GAAAA,CAAAA,cAAAA,GAAAA,IAAAA,CAAAA,OAAL,EAAa+K,SAAb,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,qBAAA,CAAA,IAAA,CAAA,cAAA,EAAyB,KAAKjH,aAAL,CAAmBwC,IAA5C,EAAmD,IAAnD,CAAA,CAAA;AACD,OAHD,MAGO,IAAI1E,aAAa,CAACgJ,OAAlB,EAA2B;AAAA,QAAA,IAAA,qBAAA,EAAA,cAAA,EAAA,sBAAA,EAAA,cAAA,CAAA;;AAChC,QAAA,CAAA,qBAAA,GAAA,CAAA,cAAA,GAAA,IAAA,CAAK5K,OAAL,EAAa4K,OAAb,gEAAuB,IAAK9G,CAAAA,aAAL,CAAmBzB,KAA1C,CAAA,CAAA;QACA,CAAKrC,sBAAAA,GAAAA,CAAAA,cAAAA,GAAAA,IAAAA,CAAAA,OAAL,EAAa+K,SAAb,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,sBAAA,CAAA,IAAA,CAAA,cAAA,EAAyBjE,SAAzB,EAAoC,IAAA,CAAKhD,aAAL,CAAmBzB,KAAvD,CAAA,CAAA;AACD,OARuB;;;MAWxB,IAAIT,aAAa,CAAClB,SAAlB,EAA6B;AAC3B,QAAA,IAAA,CAAKA,SAAL,CAAe6D,OAAf,CAAuB,CAAC;AAAEyG,UAAAA,QAAAA;AAAF,SAAD,KAAkB;UACvCA,QAAQ,CAAC,IAAKlH,CAAAA,aAAN,CAAR,CAAA;SADF,CAAA,CAAA;AAGD,OAfuB;;;MAkBxB,IAAIlC,aAAa,CAACiI,KAAlB,EAAyB;AACvB,QAAA,IAAA,CAAK9J,MAAL,CAAYwC,aAAZ,EAAA,CAA4BC,MAA5B,CAAmC;UACjCE,KAAK,EAAE,KAAK9B,YADqB;AAEjC6B,UAAAA,IAAI,EAAE,wBAAA;SAFR,CAAA,CAAA;AAID,OAAA;KAvBH,CAAA,CAAA;AAyBD,GAAA;;AAjrB0D,CAAA;;AAorB7D,SAASwI,iBAAT,CACEvI,KADF,EAEE1C,OAFF,EAGW;EACT,OACEA,OAAO,CAAC4C,OAAR,KAAoB,KAApB,IACA,CAACF,KAAK,CAACuB,KAAN,CAAYgC,aADb,IAEA,EAAEvD,KAAK,CAACuB,KAAN,CAAYyD,MAAZ,KAAuB,OAAvB,IAAkC1H,OAAO,CAACkL,YAAR,KAAyB,KAA7D,CAHF,CAAA;AAKD,CAAA;;AAED,SAASpK,kBAAT,CACE4B,KADF,EAEE1C,OAFF,EAGW;EACT,OACEiL,iBAAiB,CAACvI,KAAD,EAAQ1C,OAAR,CAAjB,IACC0C,KAAK,CAACuB,KAAN,CAAYgC,aAAZ,GAA4B,CAA5B,IACC5E,aAAa,CAACqB,KAAD,EAAQ1C,OAAR,EAAiBA,OAAO,CAACmL,cAAzB,CAHjB,CAAA;AAKD,CAAA;;AAED,SAAS9J,aAAT,CACEqB,KADF,EAEE1C,OAFF,EAGEoL,KAHF,EAME;AACA,EAAA,IAAIpL,OAAO,CAAC4C,OAAR,KAAoB,KAAxB,EAA+B;AAC7B,IAAA,MAAMyI,KAAK,GAAG,OAAOD,KAAP,KAAiB,UAAjB,GAA8BA,KAAK,CAAC1I,KAAD,CAAnC,GAA6C0I,KAA3D,CAAA;AAEA,IAAA,OAAOC,KAAK,KAAK,QAAV,IAAuBA,KAAK,KAAK,KAAV,IAAmBxF,OAAO,CAACnD,KAAD,EAAQ1C,OAAR,CAAxD,CAAA;AACD,GAAA;;AACD,EAAA,OAAO,KAAP,CAAA;AACD,CAAA;;AAED,SAASiD,qBAAT,CACEP,KADF,EAEEZ,SAFF,EAGE9B,OAHF,EAIE6B,WAJF,EAKW;AACT,EAAA,OACE7B,OAAO,CAAC4C,OAAR,KAAoB,KAApB,KACCF,KAAK,KAAKZ,SAAV,IAAuBD,WAAW,CAACe,OAAZ,KAAwB,KADhD,CAEC,KAAA,CAAC5C,OAAO,CAACsL,QAAT,IAAqB5I,KAAK,CAACuB,KAAN,CAAYyD,MAAZ,KAAuB,OAF7C,KAGA7B,OAAO,CAACnD,KAAD,EAAQ1C,OAAR,CAJT,CAAA;AAMD,CAAA;;AAED,SAAS6F,OAAT,CACEnD,KADF,EAEE1C,OAFF,EAGW;AACT,EAAA,OAAO0C,KAAK,CAAC6I,aAAN,CAAoBvL,OAAO,CAACmD,SAA5B,CAAP,CAAA;AACD;AAGD;;;AACA,SAASU,qCAAT,CAOElB,QAPF,EAQE6I,gBARF,EASExL,OATF,EAgBE;AACA;AACA;AACA;AACA;AACA;AACA;EACA,IAAIA,OAAO,CAACkI,gBAAZ,EAA8B;AAC5B,IAAA,OAAO,KAAP,CAAA;AACD,GATD;AAYA;;;AACA,EAAA,IAAIlI,OAAO,CAACwI,eAAR,KAA4B1B,SAAhC,EAA2C;AACzC;AACA;AACA;IACA,OAAO0E,gBAAgB,CAAC5D,iBAAxB,CAAA;AACD,GAlBD;AAqBA;;;EACA,IAAI,CAACtF,mBAAmB,CAACK,QAAQ,CAACuB,gBAAT,EAAD,EAA8BsH,gBAA9B,CAAxB,EAAyE;AACvE,IAAA,OAAO,IAAP,CAAA;AACD,GAxBD;;;AA2BA,EAAA,OAAO,KAAP,CAAA;AACD;;;;"}