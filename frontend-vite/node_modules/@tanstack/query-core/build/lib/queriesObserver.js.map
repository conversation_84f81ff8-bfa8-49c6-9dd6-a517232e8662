{"version": 3, "file": "queriesObserver.js", "sources": ["../../src/queriesObserver.ts"], "sourcesContent": ["import { difference, replaceAt } from './utils'\nimport { notifyManager } from './notifyManager'\nimport { QueryObserver } from './queryObserver'\nimport { Subscribable } from './subscribable'\nimport type {\n  DefaultedQueryObserverOptions,\n  QueryObserverOptions,\n  QueryObserverResult,\n} from './types'\nimport type { QueryClient } from './queryClient'\nimport type { NotifyOptions } from './queryObserver'\n\ntype QueriesObserverListener = (result: QueryObserverResult[]) => void\n\nexport class QueriesObserver extends Subscribable<QueriesObserverListener> {\n  private client: QueryClient\n  private result: QueryObserverResult[]\n  private queries: QueryObserverOptions[]\n  private observers: QueryObserver[]\n  private observersMap: Record<string, QueryObserver>\n\n  constructor(client: QueryClient, queries?: QueryObserverOptions[]) {\n    super()\n\n    this.client = client\n    this.queries = []\n    this.result = []\n    this.observers = []\n    this.observersMap = {}\n\n    if (queries) {\n      this.setQueries(queries)\n    }\n  }\n\n  protected onSubscribe(): void {\n    if (this.listeners.size === 1) {\n      this.observers.forEach((observer) => {\n        observer.subscribe((result) => {\n          this.onUpdate(observer, result)\n        })\n      })\n    }\n  }\n\n  protected onUnsubscribe(): void {\n    if (!this.listeners.size) {\n      this.destroy()\n    }\n  }\n\n  destroy(): void {\n    this.listeners = new Set()\n    this.observers.forEach((observer) => {\n      observer.destroy()\n    })\n  }\n\n  setQueries(\n    queries: QueryObserverOptions[],\n    notifyOptions?: NotifyOptions,\n  ): void {\n    this.queries = queries\n\n    notifyManager.batch(() => {\n      const prevObservers = this.observers\n\n      const newObserverMatches = this.findMatchingObservers(this.queries)\n\n      // set options for the new observers to notify of changes\n      newObserverMatches.forEach((match) =>\n        match.observer.setOptions(match.defaultedQueryOptions, notifyOptions),\n      )\n\n      const newObservers = newObserverMatches.map((match) => match.observer)\n      const newObserversMap = Object.fromEntries(\n        newObservers.map((observer) => [observer.options.queryHash, observer]),\n      )\n      const newResult = newObservers.map((observer) =>\n        observer.getCurrentResult(),\n      )\n\n      const hasIndexChange = newObservers.some(\n        (observer, index) => observer !== prevObservers[index],\n      )\n      if (prevObservers.length === newObservers.length && !hasIndexChange) {\n        return\n      }\n\n      this.observers = newObservers\n      this.observersMap = newObserversMap\n      this.result = newResult\n\n      if (!this.hasListeners()) {\n        return\n      }\n\n      difference(prevObservers, newObservers).forEach((observer) => {\n        observer.destroy()\n      })\n\n      difference(newObservers, prevObservers).forEach((observer) => {\n        observer.subscribe((result) => {\n          this.onUpdate(observer, result)\n        })\n      })\n\n      this.notify()\n    })\n  }\n\n  getCurrentResult(): QueryObserverResult[] {\n    return this.result\n  }\n\n  getQueries() {\n    return this.observers.map((observer) => observer.getCurrentQuery())\n  }\n\n  getObservers() {\n    return this.observers\n  }\n\n  getOptimisticResult(queries: QueryObserverOptions[]): QueryObserverResult[] {\n    return this.findMatchingObservers(queries).map((match) =>\n      match.observer.getOptimisticResult(match.defaultedQueryOptions),\n    )\n  }\n\n  private findMatchingObservers(\n    queries: QueryObserverOptions[],\n  ): QueryObserverMatch[] {\n    const prevObservers = this.observers\n    const prevObserversMap = new Map(\n      prevObservers.map((observer) => [observer.options.queryHash, observer]),\n    )\n\n    const defaultedQueryOptions = queries.map((options) =>\n      this.client.defaultQueryOptions(options),\n    )\n\n    const matchingObservers: QueryObserverMatch[] =\n      defaultedQueryOptions.flatMap((defaultedOptions) => {\n        const match = prevObserversMap.get(defaultedOptions.queryHash)\n        if (match != null) {\n          return [{ defaultedQueryOptions: defaultedOptions, observer: match }]\n        }\n        return []\n      })\n\n    const matchedQueryHashes = new Set(\n      matchingObservers.map((match) => match.defaultedQueryOptions.queryHash),\n    )\n    const unmatchedQueries = defaultedQueryOptions.filter(\n      (defaultedOptions) => !matchedQueryHashes.has(defaultedOptions.queryHash),\n    )\n\n    const matchingObserversSet = new Set(\n      matchingObservers.map((match) => match.observer),\n    )\n    const unmatchedObservers = prevObservers.filter(\n      (prevObserver) => !matchingObserversSet.has(prevObserver),\n    )\n\n    const getObserver = (options: QueryObserverOptions): QueryObserver => {\n      const defaultedOptions = this.client.defaultQueryOptions(options)\n      const currentObserver = this.observersMap[defaultedOptions.queryHash!]\n      return currentObserver ?? new QueryObserver(this.client, defaultedOptions)\n    }\n\n    const newOrReusedObservers: QueryObserverMatch[] = unmatchedQueries.map(\n      (options, index) => {\n        if (options.keepPreviousData) {\n          // return previous data from one of the observers that no longer match\n          const previouslyUsedObserver = unmatchedObservers[index]\n          if (previouslyUsedObserver !== undefined) {\n            return {\n              defaultedQueryOptions: options,\n              observer: previouslyUsedObserver,\n            }\n          }\n        }\n        return {\n          defaultedQueryOptions: options,\n          observer: getObserver(options),\n        }\n      },\n    )\n\n    const sortMatchesByOrderOfQueries = (\n      a: QueryObserverMatch,\n      b: QueryObserverMatch,\n    ): number =>\n      defaultedQueryOptions.indexOf(a.defaultedQueryOptions) -\n      defaultedQueryOptions.indexOf(b.defaultedQueryOptions)\n\n    return matchingObservers\n      .concat(newOrReusedObservers)\n      .sort(sortMatchesByOrderOfQueries)\n  }\n\n  private onUpdate(observer: QueryObserver, result: QueryObserverResult): void {\n    const index = this.observers.indexOf(observer)\n    if (index !== -1) {\n      this.result = replaceAt(this.result, index, result)\n      this.notify()\n    }\n  }\n\n  private notify(): void {\n    notifyManager.batch(() => {\n      this.listeners.forEach(({ listener }) => {\n        listener(this.result)\n      })\n    })\n  }\n}\n\ntype QueryObserverMatch = {\n  defaultedQueryOptions: DefaultedQueryObserverOptions\n  observer: QueryObserver\n}\n"], "names": ["QueriesObserver", "Subscribable", "constructor", "client", "queries", "result", "observers", "observersMap", "setQueries", "onSubscribe", "listeners", "size", "for<PERSON>ach", "observer", "subscribe", "onUpdate", "onUnsubscribe", "destroy", "Set", "notifyOptions", "notify<PERSON><PERSON>ger", "batch", "prevObservers", "newObserverMatches", "findMatchingObservers", "match", "setOptions", "defaultedQueryOptions", "newObservers", "map", "newObserversMap", "Object", "fromEntries", "options", "queryHash", "newResult", "getCurrentResult", "hasIndexChange", "some", "index", "length", "hasListeners", "difference", "notify", "getQueries", "get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getObservers", "getOptimisticResult", "prevObserversMap", "Map", "defaultQueryOptions", "matchingObservers", "flatMap", "defaultedOptions", "get", "matchedQueryHashes", "unmatchedQueries", "filter", "has", "matchingObserversSet", "unmatchedObservers", "prevObserver", "getObserver", "currentObserver", "QueryObserver", "newOrReusedObservers", "keepPreviousData", "previouslyUsedObserver", "undefined", "sortMatchesByOrderOfQueries", "a", "b", "indexOf", "concat", "sort", "replaceAt", "listener"], "mappings": ";;;;;;;;;AAcO,MAAMA,eAAN,SAA8BC,yBAA9B,CAAoE;AAOzEC,EAAAA,WAAW,CAACC,MAAD,EAAsBC,OAAtB,EAAwD;AACjE,IAAA,KAAA,EAAA,CAAA;IAEA,IAAKD,CAAAA,MAAL,GAAcA,MAAd,CAAA;IACA,IAAKC,CAAAA,OAAL,GAAe,EAAf,CAAA;IACA,IAAKC,CAAAA,MAAL,GAAc,EAAd,CAAA;IACA,IAAKC,CAAAA,SAAL,GAAiB,EAAjB,CAAA;IACA,IAAKC,CAAAA,YAAL,GAAoB,EAApB,CAAA;;AAEA,IAAA,IAAIH,OAAJ,EAAa;MACX,IAAKI,CAAAA,UAAL,CAAgBJ,OAAhB,CAAA,CAAA;AACD,KAAA;AACF,GAAA;;AAESK,EAAAA,WAAW,GAAS;AAC5B,IAAA,IAAI,KAAKC,SAAL,CAAeC,IAAf,KAAwB,CAA5B,EAA+B;AAC7B,MAAA,IAAA,CAAKL,SAAL,CAAeM,OAAf,CAAwBC,QAAD,IAAc;AACnCA,QAAAA,QAAQ,CAACC,SAAT,CAAoBT,MAAD,IAAY;AAC7B,UAAA,IAAA,CAAKU,QAAL,CAAcF,QAAd,EAAwBR,MAAxB,CAAA,CAAA;SADF,CAAA,CAAA;OADF,CAAA,CAAA;AAKD,KAAA;AACF,GAAA;;AAESW,EAAAA,aAAa,GAAS;AAC9B,IAAA,IAAI,CAAC,IAAA,CAAKN,SAAL,CAAeC,IAApB,EAA0B;AACxB,MAAA,IAAA,CAAKM,OAAL,EAAA,CAAA;AACD,KAAA;AACF,GAAA;;AAEDA,EAAAA,OAAO,GAAS;AACd,IAAA,IAAA,CAAKP,SAAL,GAAiB,IAAIQ,GAAJ,EAAjB,CAAA;AACA,IAAA,IAAA,CAAKZ,SAAL,CAAeM,OAAf,CAAwBC,QAAD,IAAc;AACnCA,MAAAA,QAAQ,CAACI,OAAT,EAAA,CAAA;KADF,CAAA,CAAA;AAGD,GAAA;;AAEDT,EAAAA,UAAU,CACRJ,OADQ,EAERe,aAFQ,EAGF;IACN,IAAKf,CAAAA,OAAL,GAAeA,OAAf,CAAA;IAEAgB,2BAAa,CAACC,KAAd,CAAoB,MAAM;MACxB,MAAMC,aAAa,GAAG,IAAA,CAAKhB,SAA3B,CAAA;MAEA,MAAMiB,kBAAkB,GAAG,IAAKC,CAAAA,qBAAL,CAA2B,IAAKpB,CAAAA,OAAhC,CAA3B,CAHwB;;AAMxBmB,MAAAA,kBAAkB,CAACX,OAAnB,CAA4Ba,KAAD,IACzBA,KAAK,CAACZ,QAAN,CAAea,UAAf,CAA0BD,KAAK,CAACE,qBAAhC,EAAuDR,aAAvD,CADF,CAAA,CAAA;MAIA,MAAMS,YAAY,GAAGL,kBAAkB,CAACM,GAAnB,CAAwBJ,KAAD,IAAWA,KAAK,CAACZ,QAAxC,CAArB,CAAA;MACA,MAAMiB,eAAe,GAAGC,MAAM,CAACC,WAAP,CACtBJ,YAAY,CAACC,GAAb,CAAkBhB,QAAD,IAAc,CAACA,QAAQ,CAACoB,OAAT,CAAiBC,SAAlB,EAA6BrB,QAA7B,CAA/B,CADsB,CAAxB,CAAA;AAGA,MAAA,MAAMsB,SAAS,GAAGP,YAAY,CAACC,GAAb,CAAkBhB,QAAD,IACjCA,QAAQ,CAACuB,gBAAT,EADgB,CAAlB,CAAA;AAIA,MAAA,MAAMC,cAAc,GAAGT,YAAY,CAACU,IAAb,CACrB,CAACzB,QAAD,EAAW0B,KAAX,KAAqB1B,QAAQ,KAAKS,aAAa,CAACiB,KAAD,CAD1B,CAAvB,CAAA;;MAGA,IAAIjB,aAAa,CAACkB,MAAd,KAAyBZ,YAAY,CAACY,MAAtC,IAAgD,CAACH,cAArD,EAAqE;AACnE,QAAA,OAAA;AACD,OAAA;;MAED,IAAK/B,CAAAA,SAAL,GAAiBsB,YAAjB,CAAA;MACA,IAAKrB,CAAAA,YAAL,GAAoBuB,eAApB,CAAA;MACA,IAAKzB,CAAAA,MAAL,GAAc8B,SAAd,CAAA;;AAEA,MAAA,IAAI,CAAC,IAAA,CAAKM,YAAL,EAAL,EAA0B;AACxB,QAAA,OAAA;AACD,OAAA;;MAEDC,gBAAU,CAACpB,aAAD,EAAgBM,YAAhB,CAAV,CAAwChB,OAAxC,CAAiDC,QAAD,IAAc;AAC5DA,QAAAA,QAAQ,CAACI,OAAT,EAAA,CAAA;OADF,CAAA,CAAA;MAIAyB,gBAAU,CAACd,YAAD,EAAeN,aAAf,CAAV,CAAwCV,OAAxC,CAAiDC,QAAD,IAAc;AAC5DA,QAAAA,QAAQ,CAACC,SAAT,CAAoBT,MAAD,IAAY;AAC7B,UAAA,IAAA,CAAKU,QAAL,CAAcF,QAAd,EAAwBR,MAAxB,CAAA,CAAA;SADF,CAAA,CAAA;OADF,CAAA,CAAA;AAMA,MAAA,IAAA,CAAKsC,MAAL,EAAA,CAAA;KA3CF,CAAA,CAAA;AA6CD,GAAA;;AAEDP,EAAAA,gBAAgB,GAA0B;AACxC,IAAA,OAAO,KAAK/B,MAAZ,CAAA;AACD,GAAA;;AAEDuC,EAAAA,UAAU,GAAG;IACX,OAAO,IAAA,CAAKtC,SAAL,CAAeuB,GAAf,CAAoBhB,QAAD,IAAcA,QAAQ,CAACgC,eAAT,EAAjC,CAAP,CAAA;AACD,GAAA;;AAEDC,EAAAA,YAAY,GAAG;AACb,IAAA,OAAO,KAAKxC,SAAZ,CAAA;AACD,GAAA;;EAEDyC,mBAAmB,CAAC3C,OAAD,EAAyD;AAC1E,IAAA,OAAO,KAAKoB,qBAAL,CAA2BpB,OAA3B,CAAoCyB,CAAAA,GAApC,CAAyCJ,KAAD,IAC7CA,KAAK,CAACZ,QAAN,CAAekC,mBAAf,CAAmCtB,KAAK,CAACE,qBAAzC,CADK,CAAP,CAAA;AAGD,GAAA;;EAEOH,qBAAqB,CAC3BpB,OAD2B,EAEL;IACtB,MAAMkB,aAAa,GAAG,IAAA,CAAKhB,SAA3B,CAAA;IACA,MAAM0C,gBAAgB,GAAG,IAAIC,GAAJ,CACvB3B,aAAa,CAACO,GAAd,CAAmBhB,QAAD,IAAc,CAACA,QAAQ,CAACoB,OAAT,CAAiBC,SAAlB,EAA6BrB,QAA7B,CAAhC,CADuB,CAAzB,CAAA;AAIA,IAAA,MAAMc,qBAAqB,GAAGvB,OAAO,CAACyB,GAAR,CAAaI,OAAD,IACxC,IAAA,CAAK9B,MAAL,CAAY+C,mBAAZ,CAAgCjB,OAAhC,CAD4B,CAA9B,CAAA;AAIA,IAAA,MAAMkB,iBAAuC,GAC3CxB,qBAAqB,CAACyB,OAAtB,CAA+BC,gBAAD,IAAsB;MAClD,MAAM5B,KAAK,GAAGuB,gBAAgB,CAACM,GAAjB,CAAqBD,gBAAgB,CAACnB,SAAtC,CAAd,CAAA;;MACA,IAAIT,KAAK,IAAI,IAAb,EAAmB;AACjB,QAAA,OAAO,CAAC;AAAEE,UAAAA,qBAAqB,EAAE0B,gBAAzB;AAA2CxC,UAAAA,QAAQ,EAAEY,KAAAA;AAArD,SAAD,CAAP,CAAA;AACD,OAAA;;AACD,MAAA,OAAO,EAAP,CAAA;AACD,KAND,CADF,CAAA;AASA,IAAA,MAAM8B,kBAAkB,GAAG,IAAIrC,GAAJ,CACzBiC,iBAAiB,CAACtB,GAAlB,CAAuBJ,KAAD,IAAWA,KAAK,CAACE,qBAAN,CAA4BO,SAA7D,CADyB,CAA3B,CAAA;AAGA,IAAA,MAAMsB,gBAAgB,GAAG7B,qBAAqB,CAAC8B,MAAtB,CACtBJ,gBAAD,IAAsB,CAACE,kBAAkB,CAACG,GAAnB,CAAuBL,gBAAgB,CAACnB,SAAxC,CADA,CAAzB,CAAA;AAIA,IAAA,MAAMyB,oBAAoB,GAAG,IAAIzC,GAAJ,CAC3BiC,iBAAiB,CAACtB,GAAlB,CAAuBJ,KAAD,IAAWA,KAAK,CAACZ,QAAvC,CAD2B,CAA7B,CAAA;AAGA,IAAA,MAAM+C,kBAAkB,GAAGtC,aAAa,CAACmC,MAAd,CACxBI,YAAD,IAAkB,CAACF,oBAAoB,CAACD,GAArB,CAAyBG,YAAzB,CADM,CAA3B,CAAA;;IAIA,MAAMC,WAAW,GAAI7B,OAAD,IAAkD;MACpE,MAAMoB,gBAAgB,GAAG,IAAKlD,CAAAA,MAAL,CAAY+C,mBAAZ,CAAgCjB,OAAhC,CAAzB,CAAA;MACA,MAAM8B,eAAe,GAAG,IAAKxD,CAAAA,YAAL,CAAkB8C,gBAAgB,CAACnB,SAAnC,CAAxB,CAAA;MACA,OAAO6B,eAAP,IAAOA,IAAAA,GAAAA,eAAP,GAA0B,IAAIC,2BAAJ,CAAkB,IAAK7D,CAAAA,MAAvB,EAA+BkD,gBAA/B,CAA1B,CAAA;KAHF,CAAA;;IAMA,MAAMY,oBAA0C,GAAGT,gBAAgB,CAAC3B,GAAjB,CACjD,CAACI,OAAD,EAAUM,KAAV,KAAoB;MAClB,IAAIN,OAAO,CAACiC,gBAAZ,EAA8B;AAC5B;AACA,QAAA,MAAMC,sBAAsB,GAAGP,kBAAkB,CAACrB,KAAD,CAAjD,CAAA;;QACA,IAAI4B,sBAAsB,KAAKC,SAA/B,EAA0C;UACxC,OAAO;AACLzC,YAAAA,qBAAqB,EAAEM,OADlB;AAELpB,YAAAA,QAAQ,EAAEsD,sBAAAA;WAFZ,CAAA;AAID,SAAA;AACF,OAAA;;MACD,OAAO;AACLxC,QAAAA,qBAAqB,EAAEM,OADlB;QAELpB,QAAQ,EAAEiD,WAAW,CAAC7B,OAAD,CAAA;OAFvB,CAAA;AAID,KAhBgD,CAAnD,CAAA;;IAmBA,MAAMoC,2BAA2B,GAAG,CAClCC,CADkC,EAElCC,CAFkC,KAIlC5C,qBAAqB,CAAC6C,OAAtB,CAA8BF,CAAC,CAAC3C,qBAAhC,IACAA,qBAAqB,CAAC6C,OAAtB,CAA8BD,CAAC,CAAC5C,qBAAhC,CALF,CAAA;;IAOA,OAAOwB,iBAAiB,CACrBsB,MADI,CACGR,oBADH,CAEJS,CAAAA,IAFI,CAECL,2BAFD,CAAP,CAAA;AAGD,GAAA;;AAEOtD,EAAAA,QAAQ,CAACF,QAAD,EAA0BR,MAA1B,EAA6D;IAC3E,MAAMkC,KAAK,GAAG,IAAKjC,CAAAA,SAAL,CAAekE,OAAf,CAAuB3D,QAAvB,CAAd,CAAA;;AACA,IAAA,IAAI0B,KAAK,KAAK,CAAC,CAAf,EAAkB;MAChB,IAAKlC,CAAAA,MAAL,GAAcsE,eAAS,CAAC,IAAA,CAAKtE,MAAN,EAAckC,KAAd,EAAqBlC,MAArB,CAAvB,CAAA;AACA,MAAA,IAAA,CAAKsC,MAAL,EAAA,CAAA;AACD,KAAA;AACF,GAAA;;AAEOA,EAAAA,MAAM,GAAS;IACrBvB,2BAAa,CAACC,KAAd,CAAoB,MAAM;AACxB,MAAA,IAAA,CAAKX,SAAL,CAAeE,OAAf,CAAuB,CAAC;AAAEgE,QAAAA,QAAAA;AAAF,OAAD,KAAkB;QACvCA,QAAQ,CAAC,IAAKvE,CAAAA,MAAN,CAAR,CAAA;OADF,CAAA,CAAA;KADF,CAAA,CAAA;AAKD,GAAA;;AAzMwE;;;;"}