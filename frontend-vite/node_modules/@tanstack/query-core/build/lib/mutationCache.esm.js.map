{"version": 3, "file": "mutationCache.esm.js", "sources": ["../../src/mutationCache.ts"], "sourcesContent": ["import { notify<PERSON><PERSON><PERSON> } from './notifyManager'\nimport { Mutation } from './mutation'\nimport { matchMutation, noop } from './utils'\nimport { Subscribable } from './subscribable'\nimport type { MutationObserver } from './mutationObserver'\nimport type { MutationOptions, NotifyEvent } from './types'\nimport type { QueryClient } from './queryClient'\nimport type { Action, MutationState } from './mutation'\nimport type { MutationFilters } from './utils'\n\n// TYPES\n\ninterface MutationCacheConfig {\n  onError?: (\n    error: unknown,\n    variables: unknown,\n    context: unknown,\n    mutation: Mutation<unknown, unknown, unknown>,\n  ) => Promise<unknown> | unknown\n  onSuccess?: (\n    data: unknown,\n    variables: unknown,\n    context: unknown,\n    mutation: Mutation<unknown, unknown, unknown>,\n  ) => Promise<unknown> | unknown\n  onMutate?: (\n    variables: unknown,\n    mutation: Mutation<unknown, unknown, unknown>,\n  ) => Promise<unknown> | unknown\n  onSettled?: (\n    data: unknown | undefined,\n    error: unknown | null,\n    variables: unknown,\n    context: unknown,\n    mutation: Mutation<unknown, unknown, unknown>,\n  ) => Promise<unknown> | unknown\n}\n\ninterface NotifyEventMutationAdded extends NotifyEvent {\n  type: 'added'\n  mutation: Mutation<any, any, any, any>\n}\ninterface NotifyEventMutationRemoved extends NotifyEvent {\n  type: 'removed'\n  mutation: Mutation<any, any, any, any>\n}\n\ninterface NotifyEventMutationObserverAdded extends NotifyEvent {\n  type: 'observerAdded'\n  mutation: Mutation<any, any, any, any>\n  observer: MutationObserver<any, any, any>\n}\n\ninterface NotifyEventMutationObserverRemoved extends NotifyEvent {\n  type: 'observerRemoved'\n  mutation: Mutation<any, any, any, any>\n  observer: MutationObserver<any, any, any>\n}\n\ninterface NotifyEventMutationObserverOptionsUpdated extends NotifyEvent {\n  type: 'observerOptionsUpdated'\n  mutation?: Mutation<any, any, any, any>\n  observer: MutationObserver<any, any, any, any>\n}\n\ninterface NotifyEventMutationUpdated extends NotifyEvent {\n  type: 'updated'\n  mutation: Mutation<any, any, any, any>\n  action: Action<any, any, any, any>\n}\n\ntype MutationCacheNotifyEvent =\n  | NotifyEventMutationAdded\n  | NotifyEventMutationRemoved\n  | NotifyEventMutationObserverAdded\n  | NotifyEventMutationObserverRemoved\n  | NotifyEventMutationObserverOptionsUpdated\n  | NotifyEventMutationUpdated\n\ntype MutationCacheListener = (event: MutationCacheNotifyEvent) => void\n\n// CLASS\n\nexport class MutationCache extends Subscribable<MutationCacheListener> {\n  config: MutationCacheConfig\n\n  private mutations: Mutation<any, any, any, any>[]\n  private mutationId: number\n  private resuming: Promise<unknown> | undefined\n\n  constructor(config?: MutationCacheConfig) {\n    super()\n    this.config = config || {}\n    this.mutations = []\n    this.mutationId = 0\n  }\n\n  build<TData, TError, TVariables, TContext>(\n    client: QueryClient,\n    options: MutationOptions<TData, TError, TVariables, TContext>,\n    state?: MutationState<TData, TError, TVariables, TContext>,\n  ): Mutation<TData, TError, TVariables, TContext> {\n    const mutation = new Mutation({\n      mutationCache: this,\n      logger: client.getLogger(),\n      mutationId: ++this.mutationId,\n      options: client.defaultMutationOptions(options),\n      state,\n      defaultOptions: options.mutationKey\n        ? client.getMutationDefaults(options.mutationKey)\n        : undefined,\n    })\n\n    this.add(mutation)\n\n    return mutation\n  }\n\n  add(mutation: Mutation<any, any, any, any>): void {\n    this.mutations.push(mutation)\n    this.notify({ type: 'added', mutation })\n  }\n\n  remove(mutation: Mutation<any, any, any, any>): void {\n    this.mutations = this.mutations.filter((x) => x !== mutation)\n    this.notify({ type: 'removed', mutation })\n  }\n\n  clear(): void {\n    notifyManager.batch(() => {\n      this.mutations.forEach((mutation) => {\n        this.remove(mutation)\n      })\n    })\n  }\n\n  getAll(): Mutation[] {\n    return this.mutations\n  }\n\n  find<TData = unknown, TError = unknown, TVariables = any, TContext = unknown>(\n    filters: MutationFilters,\n  ): Mutation<TData, TError, TVariables, TContext> | undefined {\n    if (typeof filters.exact === 'undefined') {\n      filters.exact = true\n    }\n\n    return this.mutations.find((mutation) => matchMutation(filters, mutation))\n  }\n\n  findAll(filters: MutationFilters): Mutation[] {\n    return this.mutations.filter((mutation) => matchMutation(filters, mutation))\n  }\n\n  notify(event: MutationCacheNotifyEvent) {\n    notifyManager.batch(() => {\n      this.listeners.forEach(({ listener }) => {\n        listener(event)\n      })\n    })\n  }\n\n  resumePausedMutations(): Promise<unknown> {\n    this.resuming = (this.resuming ?? Promise.resolve())\n      .then(() => {\n        const pausedMutations = this.mutations.filter((x) => x.state.isPaused)\n        return notifyManager.batch(() =>\n          pausedMutations.reduce(\n            (promise, mutation) =>\n              promise.then(() => mutation.continue().catch(noop)),\n            Promise.resolve() as Promise<unknown>,\n          ),\n        )\n      })\n      .then(() => {\n        this.resuming = undefined\n      })\n\n    return this.resuming\n  }\n}\n"], "names": ["MutationCache", "Subscribable", "constructor", "config", "mutations", "mutationId", "build", "client", "options", "state", "mutation", "Mutation", "mutationCache", "logger", "<PERSON><PERSON><PERSON><PERSON>", "defaultMutationOptions", "defaultOptions", "<PERSON><PERSON><PERSON>", "getMutationDefaults", "undefined", "add", "push", "notify", "type", "remove", "filter", "x", "clear", "notify<PERSON><PERSON>ger", "batch", "for<PERSON>ach", "getAll", "find", "filters", "exact", "matchMutation", "findAll", "event", "listeners", "listener", "resumePausedMutations", "resuming", "Promise", "resolve", "then", "pausedMutations", "isPaused", "reduce", "promise", "continue", "catch", "noop"], "mappings": ";;;;;AAiFA;AAEO,MAAMA,aAAN,SAA4BC,YAA5B,CAAgE;EAOrEC,WAAW,CAACC,MAAD,EAA+B;AACxC,IAAA,KAAA,EAAA,CAAA;AACA,IAAA,IAAA,CAAKA,MAAL,GAAcA,MAAM,IAAI,EAAxB,CAAA;IACA,IAAKC,CAAAA,SAAL,GAAiB,EAAjB,CAAA;IACA,IAAKC,CAAAA,UAAL,GAAkB,CAAlB,CAAA;AACD,GAAA;;AAEDC,EAAAA,KAAK,CACHC,MADG,EAEHC,OAFG,EAGHC,KAHG,EAI4C;AAC/C,IAAA,MAAMC,QAAQ,GAAG,IAAIC,QAAJ,CAAa;AAC5BC,MAAAA,aAAa,EAAE,IADa;AAE5BC,MAAAA,MAAM,EAAEN,MAAM,CAACO,SAAP,EAFoB;MAG5BT,UAAU,EAAE,EAAE,IAAA,CAAKA,UAHS;AAI5BG,MAAAA,OAAO,EAAED,MAAM,CAACQ,sBAAP,CAA8BP,OAA9B,CAJmB;MAK5BC,KAL4B;AAM5BO,MAAAA,cAAc,EAAER,OAAO,CAACS,WAAR,GACZV,MAAM,CAACW,mBAAP,CAA2BV,OAAO,CAACS,WAAnC,CADY,GAEZE,SAAAA;AARwB,KAAb,CAAjB,CAAA;IAWA,IAAKC,CAAAA,GAAL,CAASV,QAAT,CAAA,CAAA;AAEA,IAAA,OAAOA,QAAP,CAAA;AACD,GAAA;;EAEDU,GAAG,CAACV,QAAD,EAA+C;AAChD,IAAA,IAAA,CAAKN,SAAL,CAAeiB,IAAf,CAAoBX,QAApB,CAAA,CAAA;AACA,IAAA,IAAA,CAAKY,MAAL,CAAY;AAAEC,MAAAA,IAAI,EAAE,OAAR;AAAiBb,MAAAA,QAAAA;KAA7B,CAAA,CAAA;AACD,GAAA;;EAEDc,MAAM,CAACd,QAAD,EAA+C;AACnD,IAAA,IAAA,CAAKN,SAAL,GAAiB,IAAKA,CAAAA,SAAL,CAAeqB,MAAf,CAAuBC,CAAD,IAAOA,CAAC,KAAKhB,QAAnC,CAAjB,CAAA;AACA,IAAA,IAAA,CAAKY,MAAL,CAAY;AAAEC,MAAAA,IAAI,EAAE,SAAR;AAAmBb,MAAAA,QAAAA;KAA/B,CAAA,CAAA;AACD,GAAA;;AAEDiB,EAAAA,KAAK,GAAS;IACZC,aAAa,CAACC,KAAd,CAAoB,MAAM;AACxB,MAAA,IAAA,CAAKzB,SAAL,CAAe0B,OAAf,CAAwBpB,QAAD,IAAc;QACnC,IAAKc,CAAAA,MAAL,CAAYd,QAAZ,CAAA,CAAA;OADF,CAAA,CAAA;KADF,CAAA,CAAA;AAKD,GAAA;;AAEDqB,EAAAA,MAAM,GAAe;AACnB,IAAA,OAAO,KAAK3B,SAAZ,CAAA;AACD,GAAA;;EAED4B,IAAI,CACFC,OADE,EAEyD;AAC3D,IAAA,IAAI,OAAOA,OAAO,CAACC,KAAf,KAAyB,WAA7B,EAA0C;MACxCD,OAAO,CAACC,KAAR,GAAgB,IAAhB,CAAA;AACD,KAAA;;AAED,IAAA,OAAO,IAAK9B,CAAAA,SAAL,CAAe4B,IAAf,CAAqBtB,QAAD,IAAcyB,aAAa,CAACF,OAAD,EAAUvB,QAAV,CAA/C,CAAP,CAAA;AACD,GAAA;;EAED0B,OAAO,CAACH,OAAD,EAAuC;AAC5C,IAAA,OAAO,IAAK7B,CAAAA,SAAL,CAAeqB,MAAf,CAAuBf,QAAD,IAAcyB,aAAa,CAACF,OAAD,EAAUvB,QAAV,CAAjD,CAAP,CAAA;AACD,GAAA;;EAEDY,MAAM,CAACe,KAAD,EAAkC;IACtCT,aAAa,CAACC,KAAd,CAAoB,MAAM;AACxB,MAAA,IAAA,CAAKS,SAAL,CAAeR,OAAf,CAAuB,CAAC;AAAES,QAAAA,QAAAA;AAAF,OAAD,KAAkB;QACvCA,QAAQ,CAACF,KAAD,CAAR,CAAA;OADF,CAAA,CAAA;KADF,CAAA,CAAA;AAKD,GAAA;;AAEDG,EAAAA,qBAAqB,GAAqB;AAAA,IAAA,IAAA,cAAA,CAAA;;AACxC,IAAA,IAAA,CAAKC,QAAL,GAAgB,CAAC,CAAA,cAAA,GAAA,IAAA,CAAKA,QAAN,KAAA,IAAA,GAAA,cAAA,GAAkBC,OAAO,CAACC,OAAR,EAAlB,EACbC,IADa,CACR,MAAM;AACV,MAAA,MAAMC,eAAe,GAAG,IAAKzC,CAAAA,SAAL,CAAeqB,MAAf,CAAuBC,CAAD,IAAOA,CAAC,CAACjB,KAAF,CAAQqC,QAArC,CAAxB,CAAA;AACA,MAAA,OAAOlB,aAAa,CAACC,KAAd,CAAoB,MACzBgB,eAAe,CAACE,MAAhB,CACE,CAACC,OAAD,EAAUtC,QAAV,KACEsC,OAAO,CAACJ,IAAR,CAAa,MAAMlC,QAAQ,CAACuC,QAAT,EAAA,CAAoBC,KAApB,CAA0BC,IAA1B,CAAnB,CAFJ,EAGET,OAAO,CAACC,OAAR,EAHF,CADK,CAAP,CAAA;KAHY,CAAA,CAWbC,IAXa,CAWR,MAAM;MACV,IAAKH,CAAAA,QAAL,GAAgBtB,SAAhB,CAAA;AACD,KAba,CAAhB,CAAA;AAeA,IAAA,OAAO,KAAKsB,QAAZ,CAAA;AACD,GAAA;;AAhGoE;;;;"}