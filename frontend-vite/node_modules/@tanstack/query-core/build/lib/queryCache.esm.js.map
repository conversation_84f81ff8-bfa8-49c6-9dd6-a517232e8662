{"version": 3, "file": "queryCache.esm.js", "sources": ["../../src/queryCache.ts"], "sourcesContent": ["import { hashQueryKeyByOptions, matchQuery, parseFilterArgs } from './utils'\nimport { Query } from './query'\nimport { notifyManager } from './notifyManager'\nimport { Subscribable } from './subscribable'\nimport type { QueryFilters } from './utils'\nimport type { Action, QueryState } from './query'\nimport type { NotifyEvent, OmitKeyof, QueryKey, QueryOptions } from './types'\nimport type { QueryClient } from './queryClient'\nimport type { QueryObserver } from './queryObserver'\n\n// TYPES\n\ninterface QueryCacheConfig {\n  onError?: (error: unknown, query: Query<unknown, unknown, unknown>) => void\n  onSuccess?: (data: unknown, query: Query<unknown, unknown, unknown>) => void\n  onSettled?: (\n    data: unknown | undefined,\n    error: unknown | null,\n    query: Query<unknown, unknown, unknown>,\n  ) => void\n}\n\ninterface QueryHashMap {\n  [hash: string]: Query<any, any, any, any>\n}\n\ninterface NotifyEventQueryAdded extends NotifyEvent {\n  type: 'added'\n  query: Query<any, any, any, any>\n}\n\ninterface NotifyEventQueryRemoved extends NotifyEvent {\n  type: 'removed'\n  query: Query<any, any, any, any>\n}\n\ninterface NotifyEventQueryUpdated extends NotifyEvent {\n  type: 'updated'\n  query: Query<any, any, any, any>\n  action: Action<any, any>\n}\n\ninterface NotifyEventQueryObserverAdded extends NotifyEvent {\n  type: 'observerAdded'\n  query: Query<any, any, any, any>\n  observer: QueryObserver<any, any, any, any, any>\n}\n\ninterface NotifyEventQueryObserverRemoved extends NotifyEvent {\n  type: 'observerRemoved'\n  query: Query<any, any, any, any>\n  observer: QueryObserver<any, any, any, any, any>\n}\n\ninterface NotifyEventQueryObserverResultsUpdated extends NotifyEvent {\n  type: 'observerResultsUpdated'\n  query: Query<any, any, any, any>\n}\n\ninterface NotifyEventQueryObserverOptionsUpdated extends NotifyEvent {\n  type: 'observerOptionsUpdated'\n  query: Query<any, any, any, any>\n  observer: QueryObserver<any, any, any, any, any>\n}\n\nexport type QueryCacheNotifyEvent =\n  | NotifyEventQueryAdded\n  | NotifyEventQueryRemoved\n  | NotifyEventQueryUpdated\n  | NotifyEventQueryObserverAdded\n  | NotifyEventQueryObserverRemoved\n  | NotifyEventQueryObserverResultsUpdated\n  | NotifyEventQueryObserverOptionsUpdated\n\ntype QueryCacheListener = (event: QueryCacheNotifyEvent) => void\n\n// CLASS\n\nexport class QueryCache extends Subscribable<QueryCacheListener> {\n  config: QueryCacheConfig\n\n  private queries: Query<any, any, any, any>[]\n  private queriesMap: QueryHashMap\n\n  constructor(config?: QueryCacheConfig) {\n    super()\n    this.config = config || {}\n    this.queries = []\n    this.queriesMap = {}\n  }\n\n  build<TQueryFnData, TError, TData, TQueryKey extends QueryKey>(\n    client: QueryClient,\n    options: QueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    state?: QueryState<TData, TError>,\n  ): Query<TQueryFnData, TError, TData, TQueryKey> {\n    const queryKey = options.queryKey!\n    const queryHash =\n      options.queryHash ?? hashQueryKeyByOptions(queryKey, options)\n    let query = this.get<TQueryFnData, TError, TData, TQueryKey>(queryHash)\n\n    if (!query) {\n      query = new Query({\n        cache: this,\n        logger: client.getLogger(),\n        queryKey,\n        queryHash,\n        options: client.defaultQueryOptions(options),\n        state,\n        defaultOptions: client.getQueryDefaults(queryKey),\n      })\n      this.add(query)\n    }\n\n    return query\n  }\n\n  add(query: Query<any, any, any, any>): void {\n    if (!this.queriesMap[query.queryHash]) {\n      this.queriesMap[query.queryHash] = query\n      this.queries.push(query)\n      this.notify({\n        type: 'added',\n        query,\n      })\n    }\n  }\n\n  remove(query: Query<any, any, any, any>): void {\n    const queryInMap = this.queriesMap[query.queryHash]\n\n    if (queryInMap) {\n      query.destroy()\n\n      this.queries = this.queries.filter((x) => x !== query)\n\n      if (queryInMap === query) {\n        delete this.queriesMap[query.queryHash]\n      }\n\n      this.notify({ type: 'removed', query })\n    }\n  }\n\n  clear(): void {\n    notifyManager.batch(() => {\n      this.queries.forEach((query) => {\n        this.remove(query)\n      })\n    })\n  }\n\n  get<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    queryHash: string,\n  ): Query<TQueryFnData, TError, TData, TQueryKey> | undefined {\n    return this.queriesMap[queryHash]\n  }\n\n  getAll(): Query[] {\n    return this.queries\n  }\n\n  find<TQueryFnData = unknown, TError = unknown, TData = TQueryFnData>(\n    filters: QueryFilters,\n  ): Query<TQueryFnData, TError, TData> | undefined\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  find<TQueryFnData = unknown, TError = unknown, TData = TQueryFnData>(\n    queryKey: QueryKey,\n    filters?: OmitKeyof<QueryFilters, 'queryKey'>,\n  ): Query<TQueryFnData, TError, TData> | undefined\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  find<TQueryFnData = unknown, TError = unknown, TData = TQueryFnData>(\n    arg1: QueryKey | QueryFilters,\n    arg2?: OmitKeyof<QueryFilters, 'queryKey'>,\n  ): Query<TQueryFnData, TError, TData> | undefined {\n    const [filters] = parseFilterArgs(arg1, arg2)\n\n    if (typeof filters.exact === 'undefined') {\n      filters.exact = true\n    }\n\n    return this.queries.find((query) => matchQuery(filters, query))\n  }\n\n  findAll(filters?: QueryFilters): Query[]\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  findAll(\n    queryKey?: QueryKey,\n    filters?: OmitKeyof<QueryFilters, 'queryKey'>,\n  ): Query[]\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  findAll(\n    arg1?: QueryKey | QueryFilters,\n    arg2?: OmitKeyof<QueryFilters, 'queryKey'>,\n  ): Query[]\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  findAll(\n    arg1?: QueryKey | QueryFilters,\n    arg2?: OmitKeyof<QueryFilters, 'queryKey'>,\n  ): Query[] {\n    const [filters] = parseFilterArgs(arg1, arg2)\n    return Object.keys(filters).length > 0\n      ? this.queries.filter((query) => matchQuery(filters, query))\n      : this.queries\n  }\n\n  notify(event: QueryCacheNotifyEvent) {\n    notifyManager.batch(() => {\n      this.listeners.forEach(({ listener }) => {\n        listener(event)\n      })\n    })\n  }\n\n  onFocus(): void {\n    notifyManager.batch(() => {\n      this.queries.forEach((query) => {\n        query.onFocus()\n      })\n    })\n  }\n\n  onOnline(): void {\n    notifyManager.batch(() => {\n      this.queries.forEach((query) => {\n        query.onOnline()\n      })\n    })\n  }\n}\n"], "names": ["Query<PERSON>ache", "Subscribable", "constructor", "config", "queries", "queriesMap", "build", "client", "options", "state", "query<PERSON><PERSON>", "queryHash", "hashQueryKeyByOptions", "query", "get", "Query", "cache", "logger", "<PERSON><PERSON><PERSON><PERSON>", "defaultQueryOptions", "defaultOptions", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "add", "push", "notify", "type", "remove", "queryInMap", "destroy", "filter", "x", "clear", "notify<PERSON><PERSON>ger", "batch", "for<PERSON>ach", "getAll", "find", "arg1", "arg2", "filters", "parseFilter<PERSON><PERSON>s", "exact", "matchQuery", "findAll", "Object", "keys", "length", "event", "listeners", "listener", "onFocus", "onOnline"], "mappings": ";;;;;AA4EA;AAEO,MAAMA,UAAN,SAAyBC,YAAzB,CAA0D;EAM/DC,WAAW,CAACC,MAAD,EAA4B;AACrC,IAAA,KAAA,EAAA,CAAA;AACA,IAAA,IAAA,CAAKA,MAAL,GAAcA,MAAM,IAAI,EAAxB,CAAA;IACA,IAAKC,CAAAA,OAAL,GAAe,EAAf,CAAA;IACA,IAAKC,CAAAA,UAAL,GAAkB,EAAlB,CAAA;AACD,GAAA;;AAEDC,EAAAA,KAAK,CACHC,MADG,EAEHC,OAFG,EAGHC,KAHG,EAI4C;AAAA,IAAA,IAAA,kBAAA,CAAA;;AAC/C,IAAA,MAAMC,QAAQ,GAAGF,OAAO,CAACE,QAAzB,CAAA;IACA,MAAMC,SAAS,GACbH,CAAAA,kBAAAA,GAAAA,OAAO,CAACG,SADK,KACQC,IAAAA,GAAAA,kBAAAA,GAAAA,qBAAqB,CAACF,QAAD,EAAWF,OAAX,CAD5C,CAAA;AAEA,IAAA,IAAIK,KAAK,GAAG,IAAA,CAAKC,GAAL,CAAiDH,SAAjD,CAAZ,CAAA;;IAEA,IAAI,CAACE,KAAL,EAAY;MACVA,KAAK,GAAG,IAAIE,KAAJ,CAAU;AAChBC,QAAAA,KAAK,EAAE,IADS;AAEhBC,QAAAA,MAAM,EAAEV,MAAM,CAACW,SAAP,EAFQ;QAGhBR,QAHgB;QAIhBC,SAJgB;AAKhBH,QAAAA,OAAO,EAAED,MAAM,CAACY,mBAAP,CAA2BX,OAA3B,CALO;QAMhBC,KANgB;AAOhBW,QAAAA,cAAc,EAAEb,MAAM,CAACc,gBAAP,CAAwBX,QAAxB,CAAA;AAPA,OAAV,CAAR,CAAA;MASA,IAAKY,CAAAA,GAAL,CAAST,KAAT,CAAA,CAAA;AACD,KAAA;;AAED,IAAA,OAAOA,KAAP,CAAA;AACD,GAAA;;EAEDS,GAAG,CAACT,KAAD,EAAyC;IAC1C,IAAI,CAAC,KAAKR,UAAL,CAAgBQ,KAAK,CAACF,SAAtB,CAAL,EAAuC;AACrC,MAAA,IAAA,CAAKN,UAAL,CAAgBQ,KAAK,CAACF,SAAtB,IAAmCE,KAAnC,CAAA;AACA,MAAA,IAAA,CAAKT,OAAL,CAAamB,IAAb,CAAkBV,KAAlB,CAAA,CAAA;AACA,MAAA,IAAA,CAAKW,MAAL,CAAY;AACVC,QAAAA,IAAI,EAAE,OADI;AAEVZ,QAAAA,KAAAA;OAFF,CAAA,CAAA;AAID,KAAA;AACF,GAAA;;EAEDa,MAAM,CAACb,KAAD,EAAyC;IAC7C,MAAMc,UAAU,GAAG,IAAKtB,CAAAA,UAAL,CAAgBQ,KAAK,CAACF,SAAtB,CAAnB,CAAA;;AAEA,IAAA,IAAIgB,UAAJ,EAAgB;AACdd,MAAAA,KAAK,CAACe,OAAN,EAAA,CAAA;AAEA,MAAA,IAAA,CAAKxB,OAAL,GAAe,IAAKA,CAAAA,OAAL,CAAayB,MAAb,CAAqBC,CAAD,IAAOA,CAAC,KAAKjB,KAAjC,CAAf,CAAA;;MAEA,IAAIc,UAAU,KAAKd,KAAnB,EAA0B;AACxB,QAAA,OAAO,KAAKR,UAAL,CAAgBQ,KAAK,CAACF,SAAtB,CAAP,CAAA;AACD,OAAA;;AAED,MAAA,IAAA,CAAKa,MAAL,CAAY;AAAEC,QAAAA,IAAI,EAAE,SAAR;AAAmBZ,QAAAA,KAAAA;OAA/B,CAAA,CAAA;AACD,KAAA;AACF,GAAA;;AAEDkB,EAAAA,KAAK,GAAS;IACZC,aAAa,CAACC,KAAd,CAAoB,MAAM;AACxB,MAAA,IAAA,CAAK7B,OAAL,CAAa8B,OAAb,CAAsBrB,KAAD,IAAW;QAC9B,IAAKa,CAAAA,MAAL,CAAYb,KAAZ,CAAA,CAAA;OADF,CAAA,CAAA;KADF,CAAA,CAAA;AAKD,GAAA;;EAEDC,GAAG,CAMDH,SANC,EAO0D;AAC3D,IAAA,OAAO,IAAKN,CAAAA,UAAL,CAAgBM,SAAhB,CAAP,CAAA;AACD,GAAA;;AAEDwB,EAAAA,MAAM,GAAY;AAChB,IAAA,OAAO,KAAK/B,OAAZ,CAAA;AACD,GAAA;;AAYD;AACF;AACA;AACEgC,EAAAA,IAAI,CACFC,IADE,EAEFC,IAFE,EAG8C;IAChD,MAAM,CAACC,OAAD,CAAYC,GAAAA,eAAe,CAACH,IAAD,EAAOC,IAAP,CAAjC,CAAA;;AAEA,IAAA,IAAI,OAAOC,OAAO,CAACE,KAAf,KAAyB,WAA7B,EAA0C;MACxCF,OAAO,CAACE,KAAR,GAAgB,IAAhB,CAAA;AACD,KAAA;;AAED,IAAA,OAAO,IAAKrC,CAAAA,OAAL,CAAagC,IAAb,CAAmBvB,KAAD,IAAW6B,UAAU,CAACH,OAAD,EAAU1B,KAAV,CAAvC,CAAP,CAAA;AACD,GAAA;;AAiBD;AACF;AACA;AACE8B,EAAAA,OAAO,CACLN,IADK,EAELC,IAFK,EAGI;IACT,MAAM,CAACC,OAAD,CAAYC,GAAAA,eAAe,CAACH,IAAD,EAAOC,IAAP,CAAjC,CAAA;IACA,OAAOM,MAAM,CAACC,IAAP,CAAYN,OAAZ,CAAqBO,CAAAA,MAArB,GAA8B,CAA9B,GACH,IAAA,CAAK1C,OAAL,CAAayB,MAAb,CAAqBhB,KAAD,IAAW6B,UAAU,CAACH,OAAD,EAAU1B,KAAV,CAAzC,CADG,GAEH,IAAA,CAAKT,OAFT,CAAA;AAGD,GAAA;;EAEDoB,MAAM,CAACuB,KAAD,EAA+B;IACnCf,aAAa,CAACC,KAAd,CAAoB,MAAM;AACxB,MAAA,IAAA,CAAKe,SAAL,CAAed,OAAf,CAAuB,CAAC;AAAEe,QAAAA,QAAAA;AAAF,OAAD,KAAkB;QACvCA,QAAQ,CAACF,KAAD,CAAR,CAAA;OADF,CAAA,CAAA;KADF,CAAA,CAAA;AAKD,GAAA;;AAEDG,EAAAA,OAAO,GAAS;IACdlB,aAAa,CAACC,KAAd,CAAoB,MAAM;AACxB,MAAA,IAAA,CAAK7B,OAAL,CAAa8B,OAAb,CAAsBrB,KAAD,IAAW;AAC9BA,QAAAA,KAAK,CAACqC,OAAN,EAAA,CAAA;OADF,CAAA,CAAA;KADF,CAAA,CAAA;AAKD,GAAA;;AAEDC,EAAAA,QAAQ,GAAS;IACfnB,aAAa,CAACC,KAAd,CAAoB,MAAM;AACxB,MAAA,IAAA,CAAK7B,OAAL,CAAa8B,OAAb,CAAsBrB,KAAD,IAAW;AAC9BA,QAAAA,KAAK,CAACsC,QAAN,EAAA,CAAA;OADF,CAAA,CAAA;KADF,CAAA,CAAA;AAKD,GAAA;;AArK8D;;;;"}