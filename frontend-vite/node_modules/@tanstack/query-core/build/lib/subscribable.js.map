{"version": 3, "file": "subscribable.js", "sources": ["../../src/subscribable.ts"], "sourcesContent": ["type Listener = () => void\n\nexport class Subscribable<TListener extends Function = Listener> {\n  protected listeners: Set<{ listener: TListener }>\n\n  constructor() {\n    this.listeners = new Set()\n    this.subscribe = this.subscribe.bind(this)\n  }\n\n  subscribe(listener: TListener): () => void {\n    const identity = { listener }\n    this.listeners.add(identity)\n\n    this.onSubscribe()\n\n    return () => {\n      this.listeners.delete(identity)\n      this.onUnsubscribe()\n    }\n  }\n\n  hasListeners(): boolean {\n    return this.listeners.size > 0\n  }\n\n  protected onSubscribe(): void {\n    // Do nothing\n  }\n\n  protected onUnsubscribe(): void {\n    // Do nothing\n  }\n}\n"], "names": ["Subscribable", "constructor", "listeners", "Set", "subscribe", "bind", "listener", "identity", "add", "onSubscribe", "delete", "onUnsubscribe", "hasListeners", "size"], "mappings": ";;;;AAEO,MAAMA,YAAN,CAA0D;AAG/DC,EAAAA,WAAW,GAAG;AACZ,IAAA,IAAA,CAAKC,SAAL,GAAiB,IAAIC,GAAJ,EAAjB,CAAA;IACA,IAAKC,CAAAA,SAAL,GAAiB,IAAKA,CAAAA,SAAL,CAAeC,IAAf,CAAoB,IAApB,CAAjB,CAAA;AACD,GAAA;;EAEDD,SAAS,CAACE,QAAD,EAAkC;AACzC,IAAA,MAAMC,QAAQ,GAAG;AAAED,MAAAA,QAAAA;KAAnB,CAAA;AACA,IAAA,IAAA,CAAKJ,SAAL,CAAeM,GAAf,CAAmBD,QAAnB,CAAA,CAAA;AAEA,IAAA,IAAA,CAAKE,WAAL,EAAA,CAAA;AAEA,IAAA,OAAO,MAAM;AACX,MAAA,IAAA,CAAKP,SAAL,CAAeQ,MAAf,CAAsBH,QAAtB,CAAA,CAAA;AACA,MAAA,IAAA,CAAKI,aAAL,EAAA,CAAA;KAFF,CAAA;AAID,GAAA;;AAEDC,EAAAA,YAAY,GAAY;AACtB,IAAA,OAAO,IAAKV,CAAAA,SAAL,CAAeW,IAAf,GAAsB,CAA7B,CAAA;AACD,GAAA;;AAESJ,EAAAA,WAAW,GAAS;AAE7B,GAAA;;AAESE,EAAAA,aAAa,GAAS;AAE/B,GAAA;;AA9B8D;;;;"}