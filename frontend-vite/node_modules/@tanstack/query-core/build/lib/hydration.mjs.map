{"version": 3, "file": "hydration.mjs", "sources": ["../../src/hydration.ts"], "sourcesContent": ["import type { QueryClient } from './queryClient'\nimport type { Query, QueryState } from './query'\nimport type {\n  MutationKey,\n  MutationOptions,\n  QueryKey,\n  QueryOptions,\n} from './types'\nimport type { Mutation, MutationState } from './mutation'\n\n// TYPES\n\nexport interface DehydrateOptions {\n  dehydrateMutations?: boolean\n  dehydrateQueries?: boolean\n  shouldDehydrateMutation?: ShouldDehydrateMutationFunction\n  shouldDehydrateQuery?: ShouldDehydrateQueryFunction\n}\n\nexport interface HydrateOptions {\n  defaultOptions?: {\n    queries?: QueryOptions\n    mutations?: MutationOptions\n  }\n}\n\ninterface DehydratedMutation {\n  mutationKey?: MutationKey\n  state: MutationState\n}\n\ninterface DehydratedQuery {\n  queryHash: string\n  queryKey: QueryKey\n  state: QueryState\n}\n\nexport interface DehydratedState {\n  mutations: DehydratedMutation[]\n  queries: DehydratedQuery[]\n}\n\nexport type ShouldDehydrateQueryFunction = (query: Query) => boolean\n\nexport type ShouldDehydrateMutationFunction = (mutation: Mutation) => boolean\n\n// FUNCTIONS\n\nfunction dehydrateMutation(mutation: Mutation): DehydratedMutation {\n  return {\n    mutationKey: mutation.options.mutationKey,\n    state: mutation.state,\n  }\n}\n\n// Most config is not dehydrated but instead meant to configure again when\n// consuming the de/rehydrated data, typically with useQuery on the client.\n// Sometimes it might make sense to prefetch data on the server and include\n// in the html-payload, but not consume it on the initial render.\nfunction dehydrateQuery(query: Query): DehydratedQuery {\n  return {\n    state: query.state,\n    queryKey: query.queryKey,\n    queryHash: query.queryHash,\n  }\n}\n\nexport function defaultShouldDehydrateMutation(mutation: Mutation) {\n  return mutation.state.isPaused\n}\n\nexport function defaultShouldDehydrateQuery(query: Query) {\n  return query.state.status === 'success'\n}\n\nexport function dehydrate(\n  client: QueryClient,\n  options: DehydrateOptions = {},\n): DehydratedState {\n  const mutations: DehydratedMutation[] = []\n  const queries: DehydratedQuery[] = []\n\n  if (options.dehydrateMutations !== false) {\n    const shouldDehydrateMutation =\n      options.shouldDehydrateMutation || defaultShouldDehydrateMutation\n\n    client\n      .getMutationCache()\n      .getAll()\n      .forEach((mutation) => {\n        if (shouldDehydrateMutation(mutation)) {\n          mutations.push(dehydrateMutation(mutation))\n        }\n      })\n  }\n\n  if (options.dehydrateQueries !== false) {\n    const shouldDehydrateQuery =\n      options.shouldDehydrateQuery || defaultShouldDehydrateQuery\n\n    client\n      .getQueryCache()\n      .getAll()\n      .forEach((query) => {\n        if (shouldDehydrateQuery(query)) {\n          queries.push(dehydrateQuery(query))\n        }\n      })\n  }\n\n  return { mutations, queries }\n}\n\nexport function hydrate(\n  client: QueryClient,\n  dehydratedState: unknown,\n  options?: HydrateOptions,\n): void {\n  if (typeof dehydratedState !== 'object' || dehydratedState === null) {\n    return\n  }\n\n  const mutationCache = client.getMutationCache()\n  const queryCache = client.getQueryCache()\n\n  // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n  const mutations = (dehydratedState as DehydratedState).mutations || []\n  // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n  const queries = (dehydratedState as DehydratedState).queries || []\n\n  mutations.forEach((dehydratedMutation) => {\n    mutationCache.build(\n      client,\n      {\n        ...options?.defaultOptions?.mutations,\n        mutationKey: dehydratedMutation.mutationKey,\n      },\n      dehydratedMutation.state,\n    )\n  })\n\n  queries.forEach(({ queryKey, state, queryHash }) => {\n    const query = queryCache.get(queryHash)\n\n    // Do not hydrate if an existing query exists with newer data\n    if (query) {\n      if (query.state.dataUpdatedAt < state.dataUpdatedAt) {\n        // omit fetchStatus from dehydrated state\n        // so that query stays in its current fetchStatus\n        const { fetchStatus: _ignored, ...dehydratedQueryState } = state\n        query.setState(dehydratedQueryState)\n      }\n      return\n    }\n\n    // Restore query\n    queryCache.build(\n      client,\n      {\n        ...options?.defaultOptions?.queries,\n        queryKey,\n        queryHash,\n      },\n      // Reset fetch status to idle to avoid\n      // query being stuck in fetching state upon hydration\n      {\n        ...state,\n        fetchStatus: 'idle',\n      },\n    )\n  })\n}\n"], "names": ["dehydrateMutation", "mutation", "<PERSON><PERSON><PERSON>", "options", "state", "dehydrate<PERSON><PERSON>y", "query", "query<PERSON><PERSON>", "queryHash", "defaultShouldDehydrateMutation", "isPaused", "defaultShouldDehydrateQuery", "status", "dehydrate", "client", "mutations", "queries", "dehydrateMutations", "shouldDehydrateMutation", "getMutationCache", "getAll", "for<PERSON>ach", "push", "dehydrateQueries", "shouldDehydrateQuery", "get<PERSON><PERSON><PERSON><PERSON>ache", "hydrate", "dehydratedState", "mutationCache", "queryCache", "dehydratedMutation", "build", "defaultOptions", "get", "dataUpdatedAt", "fetchStatus", "_ignored", "dehydratedQueryState", "setState"], "mappings": "AAUA;AAoCA;AAEA,SAASA,iBAAT,CAA2BC,QAA3B,EAAmE;EACjE,OAAO;AACLC,IAAAA,WAAW,EAAED,QAAQ,CAACE,OAAT,CAAiBD,WADzB;IAELE,KAAK,EAAEH,QAAQ,CAACG,KAAAA;GAFlB,CAAA;AAID;AAGD;AACA;AACA;;;AACA,SAASC,cAAT,CAAwBC,KAAxB,EAAuD;EACrD,OAAO;IACLF,KAAK,EAAEE,KAAK,CAACF,KADR;IAELG,QAAQ,EAAED,KAAK,CAACC,QAFX;IAGLC,SAAS,EAAEF,KAAK,CAACE,SAAAA;GAHnB,CAAA;AAKD,CAAA;;AAEM,SAASC,8BAAT,CAAwCR,QAAxC,EAA4D;AACjE,EAAA,OAAOA,QAAQ,CAACG,KAAT,CAAeM,QAAtB,CAAA;AACD,CAAA;AAEM,SAASC,2BAAT,CAAqCL,KAArC,EAAmD;AACxD,EAAA,OAAOA,KAAK,CAACF,KAAN,CAAYQ,MAAZ,KAAuB,SAA9B,CAAA;AACD,CAAA;AAEM,SAASC,SAAT,CACLC,MADK,EAELX,OAAyB,GAAG,EAFvB,EAGY;EACjB,MAAMY,SAA+B,GAAG,EAAxC,CAAA;EACA,MAAMC,OAA0B,GAAG,EAAnC,CAAA;;AAEA,EAAA,IAAIb,OAAO,CAACc,kBAAR,KAA+B,KAAnC,EAA0C;AACxC,IAAA,MAAMC,uBAAuB,GAC3Bf,OAAO,CAACe,uBAAR,IAAmCT,8BADrC,CAAA;IAGAK,MAAM,CACHK,gBADH,EAEGC,CAAAA,MAFH,GAGGC,OAHH,CAGYpB,QAAD,IAAc;AACrB,MAAA,IAAIiB,uBAAuB,CAACjB,QAAD,CAA3B,EAAuC;AACrCc,QAAAA,SAAS,CAACO,IAAV,CAAetB,iBAAiB,CAACC,QAAD,CAAhC,CAAA,CAAA;AACD,OAAA;KANL,CAAA,CAAA;AAQD,GAAA;;AAED,EAAA,IAAIE,OAAO,CAACoB,gBAAR,KAA6B,KAAjC,EAAwC;AACtC,IAAA,MAAMC,oBAAoB,GACxBrB,OAAO,CAACqB,oBAAR,IAAgCb,2BADlC,CAAA;IAGAG,MAAM,CACHW,aADH,EAEGL,CAAAA,MAFH,GAGGC,OAHH,CAGYf,KAAD,IAAW;AAClB,MAAA,IAAIkB,oBAAoB,CAAClB,KAAD,CAAxB,EAAiC;AAC/BU,QAAAA,OAAO,CAACM,IAAR,CAAajB,cAAc,CAACC,KAAD,CAA3B,CAAA,CAAA;AACD,OAAA;KANL,CAAA,CAAA;AAQD,GAAA;;EAED,OAAO;IAAES,SAAF;AAAaC,IAAAA,OAAAA;GAApB,CAAA;AACD,CAAA;AAEM,SAASU,OAAT,CACLZ,MADK,EAELa,eAFK,EAGLxB,OAHK,EAIC;EACN,IAAI,OAAOwB,eAAP,KAA2B,QAA3B,IAAuCA,eAAe,KAAK,IAA/D,EAAqE;AACnE,IAAA,OAAA;AACD,GAAA;;AAED,EAAA,MAAMC,aAAa,GAAGd,MAAM,CAACK,gBAAP,EAAtB,CAAA;AACA,EAAA,MAAMU,UAAU,GAAGf,MAAM,CAACW,aAAP,EAAnB,CANM;;EASN,MAAMV,SAAS,GAAIY,eAAD,CAAqCZ,SAArC,IAAkD,EAApE,CATM;;AAWN,EAAA,MAAMC,OAAO,GAAIW,eAAD,CAAqCX,OAArC,IAAgD,EAAhE,CAAA;AAEAD,EAAAA,SAAS,CAACM,OAAV,CAAmBS,kBAAD,IAAwB;AAAA,IAAA,IAAA,qBAAA,CAAA;;AACxCF,IAAAA,aAAa,CAACG,KAAd,CACEjB,MADF,EAEE,EACE,IAAGX,OAAH,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,CAAA,qBAAA,GAAGA,OAAO,CAAE6B,cAAZ,KAAG,IAAA,GAAA,KAAA,CAAA,GAAA,qBAAA,CAAyBjB,SAA5B,CADF;MAEEb,WAAW,EAAE4B,kBAAkB,CAAC5B,WAAAA;KAJpC,EAME4B,kBAAkB,CAAC1B,KANrB,CAAA,CAAA;GADF,CAAA,CAAA;EAWAY,OAAO,CAACK,OAAR,CAAgB,CAAC;IAAEd,QAAF;IAAYH,KAAZ;AAAmBI,IAAAA,SAAAA;AAAnB,GAAD,KAAoC;AAAA,IAAA,IAAA,sBAAA,CAAA;;IAClD,MAAMF,KAAK,GAAGuB,UAAU,CAACI,GAAX,CAAezB,SAAf,CAAd,CADkD;;AAIlD,IAAA,IAAIF,KAAJ,EAAW;MACT,IAAIA,KAAK,CAACF,KAAN,CAAY8B,aAAZ,GAA4B9B,KAAK,CAAC8B,aAAtC,EAAqD;AACnD;AACA;QACA,MAAM;AAAEC,UAAAA,WAAW,EAAEC,QAAf;UAAyB,GAAGC,oBAAAA;AAA5B,SAAA,GAAqDjC,KAA3D,CAAA;QACAE,KAAK,CAACgC,QAAN,CAAeD,oBAAf,CAAA,CAAA;AACD,OAAA;;AACD,MAAA,OAAA;AACD,KAZiD;;;AAelDR,IAAAA,UAAU,CAACE,KAAX,CACEjB,MADF,EAEE,EACE,IAAGX,OAAH,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,CAAA,sBAAA,GAAGA,OAAO,CAAE6B,cAAZ,KAAG,IAAA,GAAA,KAAA,CAAA,GAAA,sBAAA,CAAyBhB,OAA5B,CADF;MAEET,QAFF;AAGEC,MAAAA,SAAAA;AAHF,KAFF;AAQE;AACA,IAAA,EACE,GAAGJ,KADL;AAEE+B,MAAAA,WAAW,EAAE,MAAA;KAXjB,CAAA,CAAA;GAfF,CAAA,CAAA;AA8BD;;;;"}