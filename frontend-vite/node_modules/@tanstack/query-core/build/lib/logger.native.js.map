{"version": 3, "file": "logger.native.js", "sources": ["../../src/logger.native.ts"], "sourcesContent": ["import type { Logger } from './logger'\n\n/**\n * See https://github.com/tannerlinsley/react-query/issues/795\n * and https://github.com/tannerlinsley/react-query/pull/3246/#discussion_r795105707\n */\nexport const defaultLogger: Logger = {\n  log: console.log,\n  warn: console.warn,\n  error: console.warn,\n}\n"], "names": ["defaultLogger", "log", "console", "warn", "error"], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACO,MAAMA,aAAqB,GAAG;EACnCC,GAAG,EAAEC,OAAO,CAACD,GADsB;EAEnCE,IAAI,EAAED,OAAO,CAACC,IAFqB;EAGnCC,KAAK,EAAEF,OAAO,CAACC,IAAAA;AAHoB;;;;"}