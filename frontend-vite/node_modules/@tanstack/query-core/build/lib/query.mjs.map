{"version": 3, "file": "query.mjs", "sources": ["../../src/query.ts"], "sourcesContent": ["import { getAbortController, noop, replaceData, timeUntilStale } from './utils'\nimport { defaultLogger } from './logger'\nimport { notifyManager } from './notifyManager'\nimport { canFetch, createRetryer, isCancelledError } from './retryer'\nimport { Removable } from './removable'\nimport type {\n  CancelOptions,\n  FetchStatus,\n  InitialDataFunction,\n  QueryFunctionContext,\n  QueryKey,\n  QueryMeta,\n  QueryOptions,\n  QueryStatus,\n  SetDataOptions,\n} from './types'\nimport type { QueryCache } from './queryCache'\nimport type { QueryObserver } from './queryObserver'\nimport type { Logger } from './logger'\nimport type { Retryer } from './retryer'\n\n// TYPES\n\ninterface QueryConfig<\n  TQueryFnData,\n  TError,\n  TData,\n  TQueryKey extends QueryKey = QueryKey,\n> {\n  cache: QueryCache\n  queryKey: TQueryKey\n  queryHash: string\n  logger?: Logger\n  options?: QueryOptions<TQueryFnData, TError, TData, TQueryKey>\n  defaultOptions?: QueryOptions<TQueryFnData, TError, TData, TQueryKey>\n  state?: QueryState<TData, TError>\n}\n\nexport interface QueryState<TData = unknown, TError = unknown> {\n  data: TData | undefined\n  dataUpdateCount: number\n  dataUpdatedAt: number\n  error: TError | null\n  errorUpdateCount: number\n  errorUpdatedAt: number\n  fetchFailureCount: number\n  fetchFailureReason: TError | null\n  fetchMeta: any\n  isInvalidated: boolean\n  status: QueryStatus\n  fetchStatus: FetchStatus\n}\n\nexport interface FetchContext<\n  TQueryFnData,\n  TError,\n  TData,\n  TQueryKey extends QueryKey = QueryKey,\n> {\n  fetchFn: () => unknown | Promise<unknown>\n  fetchOptions?: FetchOptions\n  signal?: AbortSignal\n  options: QueryOptions<TQueryFnData, TError, TData, any>\n  queryKey: TQueryKey\n  state: QueryState<TData, TError>\n}\n\nexport interface QueryBehavior<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n> {\n  onFetch: (\n    context: FetchContext<TQueryFnData, TError, TData, TQueryKey>,\n  ) => void\n}\n\nexport interface FetchOptions {\n  cancelRefetch?: boolean\n  meta?: any\n}\n\ninterface FailedAction<TError> {\n  type: 'failed'\n  failureCount: number\n  error: TError\n}\n\ninterface FetchAction {\n  type: 'fetch'\n  meta?: any\n}\n\ninterface SuccessAction<TData> {\n  data: TData | undefined\n  type: 'success'\n  dataUpdatedAt?: number\n  manual?: boolean\n}\n\ninterface ErrorAction<TError> {\n  type: 'error'\n  error: TError\n}\n\ninterface InvalidateAction {\n  type: 'invalidate'\n}\n\ninterface PauseAction {\n  type: 'pause'\n}\n\ninterface ContinueAction {\n  type: 'continue'\n}\n\ninterface SetStateAction<TData, TError> {\n  type: 'setState'\n  state: Partial<QueryState<TData, TError>>\n  setStateOptions?: SetStateOptions\n}\n\nexport type Action<TData, TError> =\n  | ContinueAction\n  | ErrorAction<TError>\n  | FailedAction<TError>\n  | FetchAction\n  | InvalidateAction\n  | PauseAction\n  | SetStateAction<TData, TError>\n  | SuccessAction<TData>\n\nexport interface SetStateOptions {\n  meta?: any\n}\n\n// CLASS\n\nexport class Query<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n> extends Removable {\n  queryKey: TQueryKey\n  queryHash: string\n  options!: QueryOptions<TQueryFnData, TError, TData, TQueryKey>\n  initialState: QueryState<TData, TError>\n  revertState?: QueryState<TData, TError>\n  state: QueryState<TData, TError>\n  isFetchingOptimistic?: boolean\n\n  private cache: QueryCache\n  private logger: Logger\n  private promise?: Promise<TData>\n  private retryer?: Retryer<TData>\n  private observers: QueryObserver<any, any, any, any, any>[]\n  private defaultOptions?: QueryOptions<TQueryFnData, TError, TData, TQueryKey>\n  private abortSignalConsumed: boolean\n\n  constructor(config: QueryConfig<TQueryFnData, TError, TData, TQueryKey>) {\n    super()\n\n    this.abortSignalConsumed = false\n    this.defaultOptions = config.defaultOptions\n    this.setOptions(config.options)\n    this.observers = []\n    this.cache = config.cache\n    this.logger = config.logger || defaultLogger\n    this.queryKey = config.queryKey\n    this.queryHash = config.queryHash\n    this.initialState = config.state || getDefaultState(this.options)\n    this.state = this.initialState\n    this.scheduleGc()\n  }\n\n  get meta(): QueryMeta | undefined {\n    return this.options.meta\n  }\n\n  private setOptions(\n    options?: QueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n  ): void {\n    this.options = { ...this.defaultOptions, ...options }\n\n    this.updateCacheTime(this.options.cacheTime)\n  }\n\n  protected optionalRemove() {\n    if (!this.observers.length && this.state.fetchStatus === 'idle') {\n      this.cache.remove(this)\n    }\n  }\n\n  setData(\n    newData: TData,\n    options?: SetDataOptions & { manual: boolean },\n  ): TData {\n    const data = replaceData(this.state.data, newData, this.options)\n\n    // Set data and mark it as cached\n    this.dispatch({\n      data,\n      type: 'success',\n      dataUpdatedAt: options?.updatedAt,\n      manual: options?.manual,\n    })\n\n    return data\n  }\n\n  setState(\n    state: Partial<QueryState<TData, TError>>,\n    setStateOptions?: SetStateOptions,\n  ): void {\n    this.dispatch({ type: 'setState', state, setStateOptions })\n  }\n\n  cancel(options?: CancelOptions): Promise<void> {\n    const promise = this.promise\n    this.retryer?.cancel(options)\n    return promise ? promise.then(noop).catch(noop) : Promise.resolve()\n  }\n\n  destroy(): void {\n    super.destroy()\n\n    this.cancel({ silent: true })\n  }\n\n  reset(): void {\n    this.destroy()\n    this.setState(this.initialState)\n  }\n\n  isActive(): boolean {\n    return this.observers.some((observer) => observer.options.enabled !== false)\n  }\n\n  isDisabled(): boolean {\n    return this.getObserversCount() > 0 && !this.isActive()\n  }\n\n  isStale(): boolean {\n    return (\n      this.state.isInvalidated ||\n      !this.state.dataUpdatedAt ||\n      this.observers.some((observer) => observer.getCurrentResult().isStale)\n    )\n  }\n\n  isStaleByTime(staleTime = 0): boolean {\n    return (\n      this.state.isInvalidated ||\n      !this.state.dataUpdatedAt ||\n      !timeUntilStale(this.state.dataUpdatedAt, staleTime)\n    )\n  }\n\n  onFocus(): void {\n    const observer = this.observers.find((x) => x.shouldFetchOnWindowFocus())\n\n    if (observer) {\n      observer.refetch({ cancelRefetch: false })\n    }\n\n    // Continue fetch if currently paused\n    this.retryer?.continue()\n  }\n\n  onOnline(): void {\n    const observer = this.observers.find((x) => x.shouldFetchOnReconnect())\n\n    if (observer) {\n      observer.refetch({ cancelRefetch: false })\n    }\n\n    // Continue fetch if currently paused\n    this.retryer?.continue()\n  }\n\n  addObserver(observer: QueryObserver<any, any, any, any, any>): void {\n    if (!this.observers.includes(observer)) {\n      this.observers.push(observer)\n\n      // Stop the query from being garbage collected\n      this.clearGcTimeout()\n\n      this.cache.notify({ type: 'observerAdded', query: this, observer })\n    }\n  }\n\n  removeObserver(observer: QueryObserver<any, any, any, any, any>): void {\n    if (this.observers.includes(observer)) {\n      this.observers = this.observers.filter((x) => x !== observer)\n\n      if (!this.observers.length) {\n        // If the transport layer does not support cancellation\n        // we'll let the query continue so the result can be cached\n        if (this.retryer) {\n          if (this.abortSignalConsumed) {\n            this.retryer.cancel({ revert: true })\n          } else {\n            this.retryer.cancelRetry()\n          }\n        }\n\n        this.scheduleGc()\n      }\n\n      this.cache.notify({ type: 'observerRemoved', query: this, observer })\n    }\n  }\n\n  getObserversCount(): number {\n    return this.observers.length\n  }\n\n  invalidate(): void {\n    if (!this.state.isInvalidated) {\n      this.dispatch({ type: 'invalidate' })\n    }\n  }\n\n  fetch(\n    options?: QueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    fetchOptions?: FetchOptions,\n  ): Promise<TData> {\n    if (this.state.fetchStatus !== 'idle') {\n      if (this.state.dataUpdatedAt && fetchOptions?.cancelRefetch) {\n        // Silently cancel current fetch if the user wants to cancel refetches\n        this.cancel({ silent: true })\n      } else if (this.promise) {\n        // make sure that retries that were potentially cancelled due to unmounts can continue\n        this.retryer?.continueRetry()\n        // Return current promise if we are already fetching\n        return this.promise\n      }\n    }\n\n    // Update config if passed, otherwise the config from the last execution is used\n    if (options) {\n      this.setOptions(options)\n    }\n\n    // Use the options from the first observer with a query function if no function is found.\n    // This can happen when the query is hydrated or created with setQueryData.\n    if (!this.options.queryFn) {\n      const observer = this.observers.find((x) => x.options.queryFn)\n      if (observer) {\n        this.setOptions(observer.options)\n      }\n    }\n\n    if (process.env.NODE_ENV !== 'production') {\n      if (!Array.isArray(this.options.queryKey)) {\n        this.logger.error(\n          `As of v4, queryKey needs to be an Array. If you are using a string like 'repoData', please change it to an Array, e.g. ['repoData']`,\n        )\n      }\n    }\n\n    const abortController = getAbortController()\n\n    // Create query function context\n    const queryFnContext: QueryFunctionContext<TQueryKey> = {\n      queryKey: this.queryKey,\n      pageParam: undefined,\n      meta: this.meta,\n    }\n\n    // Adds an enumerable signal property to the object that\n    // which sets abortSignalConsumed to true when the signal\n    // is read.\n    const addSignalProperty = (object: unknown) => {\n      Object.defineProperty(object, 'signal', {\n        enumerable: true,\n        get: () => {\n          if (abortController) {\n            this.abortSignalConsumed = true\n            return abortController.signal\n          }\n          return undefined\n        },\n      })\n    }\n\n    addSignalProperty(queryFnContext)\n\n    // Create fetch function\n    const fetchFn = () => {\n      if (!this.options.queryFn) {\n        return Promise.reject(\n          `Missing queryFn for queryKey '${this.options.queryHash}'`,\n        )\n      }\n      this.abortSignalConsumed = false\n      return this.options.queryFn(queryFnContext)\n    }\n\n    // Trigger behavior hook\n    const context: FetchContext<TQueryFnData, TError, TData, TQueryKey> = {\n      fetchOptions,\n      options: this.options,\n      queryKey: this.queryKey,\n      state: this.state,\n      fetchFn,\n    }\n\n    addSignalProperty(context)\n\n    this.options.behavior?.onFetch(context)\n\n    // Store state in case the current fetch needs to be reverted\n    this.revertState = this.state\n\n    // Set to fetching state if not already in it\n    if (\n      this.state.fetchStatus === 'idle' ||\n      this.state.fetchMeta !== context.fetchOptions?.meta\n    ) {\n      this.dispatch({ type: 'fetch', meta: context.fetchOptions?.meta })\n    }\n\n    const onError = (error: TError | { silent?: boolean }) => {\n      // Optimistically update state if needed\n      if (!(isCancelledError(error) && error.silent)) {\n        this.dispatch({\n          type: 'error',\n          error: error as TError,\n        })\n      }\n\n      if (!isCancelledError(error)) {\n        // Notify cache callback\n        this.cache.config.onError?.(error, this as Query<any, any, any, any>)\n        this.cache.config.onSettled?.(\n          this.state.data,\n          error,\n          this as Query<any, any, any, any>,\n        )\n\n        if (process.env.NODE_ENV !== 'production') {\n          this.logger.error(error)\n        }\n      }\n\n      if (!this.isFetchingOptimistic) {\n        // Schedule query gc after fetching\n        this.scheduleGc()\n      }\n      this.isFetchingOptimistic = false\n    }\n\n    // Try to fetch the data\n    this.retryer = createRetryer({\n      fn: context.fetchFn as () => TData,\n      abort: abortController?.abort.bind(abortController),\n      onSuccess: (data) => {\n        if (typeof data === 'undefined') {\n          if (process.env.NODE_ENV !== 'production') {\n            this.logger.error(\n              `Query data cannot be undefined. Please make sure to return a value other than undefined from your query function. Affected query key: ${this.queryHash}`,\n            )\n          }\n          onError(new Error(`${this.queryHash} data is undefined`) as any)\n          return\n        }\n\n        this.setData(data as TData)\n\n        // Notify cache callback\n        this.cache.config.onSuccess?.(data, this as Query<any, any, any, any>)\n        this.cache.config.onSettled?.(\n          data,\n          this.state.error,\n          this as Query<any, any, any, any>,\n        )\n\n        if (!this.isFetchingOptimistic) {\n          // Schedule query gc after fetching\n          this.scheduleGc()\n        }\n        this.isFetchingOptimistic = false\n      },\n      onError,\n      onFail: (failureCount, error) => {\n        this.dispatch({ type: 'failed', failureCount, error })\n      },\n      onPause: () => {\n        this.dispatch({ type: 'pause' })\n      },\n      onContinue: () => {\n        this.dispatch({ type: 'continue' })\n      },\n      retry: context.options.retry,\n      retryDelay: context.options.retryDelay,\n      networkMode: context.options.networkMode,\n    })\n\n    this.promise = this.retryer.promise\n\n    return this.promise\n  }\n\n  private dispatch(action: Action<TData, TError>): void {\n    const reducer = (\n      state: QueryState<TData, TError>,\n    ): QueryState<TData, TError> => {\n      switch (action.type) {\n        case 'failed':\n          return {\n            ...state,\n            fetchFailureCount: action.failureCount,\n            fetchFailureReason: action.error,\n          }\n        case 'pause':\n          return {\n            ...state,\n            fetchStatus: 'paused',\n          }\n        case 'continue':\n          return {\n            ...state,\n            fetchStatus: 'fetching',\n          }\n        case 'fetch':\n          return {\n            ...state,\n            fetchFailureCount: 0,\n            fetchFailureReason: null,\n            fetchMeta: action.meta ?? null,\n            fetchStatus: canFetch(this.options.networkMode)\n              ? 'fetching'\n              : 'paused',\n            ...(!state.dataUpdatedAt && {\n              error: null,\n              status: 'loading',\n            }),\n          }\n        case 'success':\n          return {\n            ...state,\n            data: action.data,\n            dataUpdateCount: state.dataUpdateCount + 1,\n            dataUpdatedAt: action.dataUpdatedAt ?? Date.now(),\n            error: null,\n            isInvalidated: false,\n            status: 'success',\n            ...(!action.manual && {\n              fetchStatus: 'idle',\n              fetchFailureCount: 0,\n              fetchFailureReason: null,\n            }),\n          }\n        case 'error':\n          const error = action.error as unknown\n\n          if (isCancelledError(error) && error.revert && this.revertState) {\n            return { ...this.revertState, fetchStatus: 'idle' }\n          }\n\n          return {\n            ...state,\n            error: error as TError,\n            errorUpdateCount: state.errorUpdateCount + 1,\n            errorUpdatedAt: Date.now(),\n            fetchFailureCount: state.fetchFailureCount + 1,\n            fetchFailureReason: error as TError,\n            fetchStatus: 'idle',\n            status: 'error',\n          }\n        case 'invalidate':\n          return {\n            ...state,\n            isInvalidated: true,\n          }\n        case 'setState':\n          return {\n            ...state,\n            ...action.state,\n          }\n      }\n    }\n\n    this.state = reducer(this.state)\n\n    notifyManager.batch(() => {\n      this.observers.forEach((observer) => {\n        observer.onQueryUpdate(action)\n      })\n\n      this.cache.notify({ query: this, type: 'updated', action })\n    })\n  }\n}\n\nfunction getDefaultState<\n  TQueryFnData,\n  TError,\n  TData,\n  TQueryKey extends QueryKey,\n>(\n  options: QueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n): QueryState<TData, TError> {\n  const data =\n    typeof options.initialData === 'function'\n      ? (options.initialData as InitialDataFunction<TData>)()\n      : options.initialData\n\n  const hasData = typeof data !== 'undefined'\n\n  const initialDataUpdatedAt = hasData\n    ? typeof options.initialDataUpdatedAt === 'function'\n      ? (options.initialDataUpdatedAt as () => number | undefined)()\n      : options.initialDataUpdatedAt\n    : 0\n\n  return {\n    data,\n    dataUpdateCount: 0,\n    dataUpdatedAt: hasData ? initialDataUpdatedAt ?? Date.now() : 0,\n    error: null,\n    errorUpdateCount: 0,\n    errorUpdatedAt: 0,\n    fetchFailureCount: 0,\n    fetchFailureReason: null,\n    fetchMeta: null,\n    isInvalidated: false,\n    status: hasData ? 'success' : 'loading',\n    fetchStatus: 'idle',\n  }\n}\n"], "names": ["Query", "Removable", "constructor", "config", "abortSignalConsumed", "defaultOptions", "setOptions", "options", "observers", "cache", "logger", "defaultLogger", "query<PERSON><PERSON>", "queryHash", "initialState", "state", "getDefaultState", "scheduleGc", "meta", "updateCacheTime", "cacheTime", "optionalRemove", "length", "fetchStatus", "remove", "setData", "newData", "data", "replaceData", "dispatch", "type", "dataUpdatedAt", "updatedAt", "manual", "setState", "setStateOptions", "cancel", "promise", "retryer", "then", "noop", "catch", "Promise", "resolve", "destroy", "silent", "reset", "isActive", "some", "observer", "enabled", "isDisabled", "getObserversCount", "isStale", "isInvalidated", "getCurrentResult", "isStaleByTime", "staleTime", "timeUntilStale", "onFocus", "find", "x", "shouldFetchOnWindowFocus", "refetch", "cancelRefetch", "continue", "onOnline", "shouldFetchOnReconnect", "addObserver", "includes", "push", "clearGcTimeout", "notify", "query", "removeObserver", "filter", "revert", "cancelRetry", "invalidate", "fetch", "fetchOptions", "continueRetry", "queryFn", "process", "env", "NODE_ENV", "Array", "isArray", "error", "abortController", "getAbortController", "queryFnContext", "pageParam", "undefined", "addSignalProperty", "object", "Object", "defineProperty", "enumerable", "get", "signal", "fetchFn", "reject", "context", "behavior", "onFetch", "revertState", "fetchMeta", "onError", "isCancelledError", "onSettled", "isFetchingOptimistic", "createRetryer", "fn", "abort", "bind", "onSuccess", "Error", "onFail", "failureCount", "onPause", "onContinue", "retry", "retry<PERSON><PERSON><PERSON>", "networkMode", "action", "reducer", "fetchFailureCount", "fetchFailureReason", "canFetch", "status", "dataUpdateCount", "Date", "now", "errorUpdateCount", "errorUpdatedAt", "notify<PERSON><PERSON>ger", "batch", "for<PERSON>ach", "onQueryUpdate", "initialData", "hasData", "initialDataUpdatedAt"], "mappings": ";;;;;;AA0IA;AAEO,MAAMA,KAAN,SAKGC,SALH,CAKa;EAiBlBC,WAAW,CAACC,MAAD,EAA8D;AACvE,IAAA,KAAA,EAAA,CAAA;IAEA,IAAKC,CAAAA,mBAAL,GAA2B,KAA3B,CAAA;AACA,IAAA,IAAA,CAAKC,cAAL,GAAsBF,MAAM,CAACE,cAA7B,CAAA;AACA,IAAA,IAAA,CAAKC,UAAL,CAAgBH,MAAM,CAACI,OAAvB,CAAA,CAAA;IACA,IAAKC,CAAAA,SAAL,GAAiB,EAAjB,CAAA;AACA,IAAA,IAAA,CAAKC,KAAL,GAAaN,MAAM,CAACM,KAApB,CAAA;AACA,IAAA,IAAA,CAAKC,MAAL,GAAcP,MAAM,CAACO,MAAP,IAAiBC,aAA/B,CAAA;AACA,IAAA,IAAA,CAAKC,QAAL,GAAgBT,MAAM,CAACS,QAAvB,CAAA;AACA,IAAA,IAAA,CAAKC,SAAL,GAAiBV,MAAM,CAACU,SAAxB,CAAA;IACA,IAAKC,CAAAA,YAAL,GAAoBX,MAAM,CAACY,KAAP,IAAgBC,eAAe,CAAC,IAAKT,CAAAA,OAAN,CAAnD,CAAA;IACA,IAAKQ,CAAAA,KAAL,GAAa,IAAA,CAAKD,YAAlB,CAAA;AACA,IAAA,IAAA,CAAKG,UAAL,EAAA,CAAA;AACD,GAAA;;AAEO,EAAA,IAAJC,IAAI,GAA0B;IAChC,OAAO,IAAA,CAAKX,OAAL,CAAaW,IAApB,CAAA;AACD,GAAA;;EAEOZ,UAAU,CAChBC,OADgB,EAEV;AACN,IAAA,IAAA,CAAKA,OAAL,GAAe,EAAE,GAAG,KAAKF,cAAV;MAA0B,GAAGE,OAAAA;KAA5C,CAAA;AAEA,IAAA,IAAA,CAAKY,eAAL,CAAqB,IAAKZ,CAAAA,OAAL,CAAaa,SAAlC,CAAA,CAAA;AACD,GAAA;;AAESC,EAAAA,cAAc,GAAG;AACzB,IAAA,IAAI,CAAC,IAAA,CAAKb,SAAL,CAAec,MAAhB,IAA0B,IAAKP,CAAAA,KAAL,CAAWQ,WAAX,KAA2B,MAAzD,EAAiE;AAC/D,MAAA,IAAA,CAAKd,KAAL,CAAWe,MAAX,CAAkB,IAAlB,CAAA,CAAA;AACD,KAAA;AACF,GAAA;;AAEDC,EAAAA,OAAO,CACLC,OADK,EAELnB,OAFK,EAGE;AACP,IAAA,MAAMoB,IAAI,GAAGC,WAAW,CAAC,KAAKb,KAAL,CAAWY,IAAZ,EAAkBD,OAAlB,EAA2B,IAAA,CAAKnB,OAAhC,CAAxB,CADO;;AAIP,IAAA,IAAA,CAAKsB,QAAL,CAAc;MACZF,IADY;AAEZG,MAAAA,IAAI,EAAE,SAFM;AAGZC,MAAAA,aAAa,EAAExB,OAAF,IAAEA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,OAAO,CAAEyB,SAHZ;AAIZC,MAAAA,MAAM,EAAE1B,OAAF,IAAEA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,OAAO,CAAE0B,MAAAA;KAJnB,CAAA,CAAA;AAOA,IAAA,OAAON,IAAP,CAAA;AACD,GAAA;;AAEDO,EAAAA,QAAQ,CACNnB,KADM,EAENoB,eAFM,EAGA;AACN,IAAA,IAAA,CAAKN,QAAL,CAAc;AAAEC,MAAAA,IAAI,EAAE,UAAR;MAAoBf,KAApB;AAA2BoB,MAAAA,eAAAA;KAAzC,CAAA,CAAA;AACD,GAAA;;EAEDC,MAAM,CAAC7B,OAAD,EAAyC;AAAA,IAAA,IAAA,aAAA,CAAA;;IAC7C,MAAM8B,OAAO,GAAG,IAAA,CAAKA,OAArB,CAAA;AACA,IAAA,CAAA,aAAA,GAAA,IAAA,CAAKC,OAAL,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,aAAA,CAAcF,MAAd,CAAqB7B,OAArB,CAAA,CAAA;AACA,IAAA,OAAO8B,OAAO,GAAGA,OAAO,CAACE,IAAR,CAAaC,IAAb,CAAA,CAAmBC,KAAnB,CAAyBD,IAAzB,CAAH,GAAoCE,OAAO,CAACC,OAAR,EAAlD,CAAA;AACD,GAAA;;AAEDC,EAAAA,OAAO,GAAS;AACd,IAAA,KAAA,CAAMA,OAAN,EAAA,CAAA;AAEA,IAAA,IAAA,CAAKR,MAAL,CAAY;AAAES,MAAAA,MAAM,EAAE,IAAA;KAAtB,CAAA,CAAA;AACD,GAAA;;AAEDC,EAAAA,KAAK,GAAS;AACZ,IAAA,IAAA,CAAKF,OAAL,EAAA,CAAA;IACA,IAAKV,CAAAA,QAAL,CAAc,IAAA,CAAKpB,YAAnB,CAAA,CAAA;AACD,GAAA;;AAEDiC,EAAAA,QAAQ,GAAY;AAClB,IAAA,OAAO,IAAKvC,CAAAA,SAAL,CAAewC,IAAf,CAAqBC,QAAD,IAAcA,QAAQ,CAAC1C,OAAT,CAAiB2C,OAAjB,KAA6B,KAA/D,CAAP,CAAA;AACD,GAAA;;AAEDC,EAAAA,UAAU,GAAY;IACpB,OAAO,IAAA,CAAKC,iBAAL,EAA2B,GAAA,CAA3B,IAAgC,CAAC,IAAA,CAAKL,QAAL,EAAxC,CAAA;AACD,GAAA;;AAEDM,EAAAA,OAAO,GAAY;IACjB,OACE,IAAA,CAAKtC,KAAL,CAAWuC,aAAX,IACA,CAAC,IAAA,CAAKvC,KAAL,CAAWgB,aADZ,IAEA,KAAKvB,SAAL,CAAewC,IAAf,CAAqBC,QAAD,IAAcA,QAAQ,CAACM,gBAAT,EAA4BF,CAAAA,OAA9D,CAHF,CAAA;AAKD,GAAA;;AAEDG,EAAAA,aAAa,CAACC,SAAS,GAAG,CAAb,EAAyB;IACpC,OACE,IAAA,CAAK1C,KAAL,CAAWuC,aAAX,IACA,CAAC,IAAA,CAAKvC,KAAL,CAAWgB,aADZ,IAEA,CAAC2B,cAAc,CAAC,IAAK3C,CAAAA,KAAL,CAAWgB,aAAZ,EAA2B0B,SAA3B,CAHjB,CAAA;AAKD,GAAA;;AAEDE,EAAAA,OAAO,GAAS;AAAA,IAAA,IAAA,cAAA,CAAA;;AACd,IAAA,MAAMV,QAAQ,GAAG,IAAKzC,CAAAA,SAAL,CAAeoD,IAAf,CAAqBC,CAAD,IAAOA,CAAC,CAACC,wBAAF,EAA3B,CAAjB,CAAA;;AAEA,IAAA,IAAIb,QAAJ,EAAc;MACZA,QAAQ,CAACc,OAAT,CAAiB;AAAEC,QAAAA,aAAa,EAAE,KAAA;OAAlC,CAAA,CAAA;AACD,KALa;;;IAQd,CAAK1B,cAAAA,GAAAA,IAAAA,CAAAA,OAAL,oCAAc2B,QAAd,EAAA,CAAA;AACD,GAAA;;AAEDC,EAAAA,QAAQ,GAAS;AAAA,IAAA,IAAA,cAAA,CAAA;;AACf,IAAA,MAAMjB,QAAQ,GAAG,IAAKzC,CAAAA,SAAL,CAAeoD,IAAf,CAAqBC,CAAD,IAAOA,CAAC,CAACM,sBAAF,EAA3B,CAAjB,CAAA;;AAEA,IAAA,IAAIlB,QAAJ,EAAc;MACZA,QAAQ,CAACc,OAAT,CAAiB;AAAEC,QAAAA,aAAa,EAAE,KAAA;OAAlC,CAAA,CAAA;AACD,KALc;;;IAQf,CAAK1B,cAAAA,GAAAA,IAAAA,CAAAA,OAAL,oCAAc2B,QAAd,EAAA,CAAA;AACD,GAAA;;EAEDG,WAAW,CAACnB,QAAD,EAAyD;IAClE,IAAI,CAAC,KAAKzC,SAAL,CAAe6D,QAAf,CAAwBpB,QAAxB,CAAL,EAAwC;AACtC,MAAA,IAAA,CAAKzC,SAAL,CAAe8D,IAAf,CAAoBrB,QAApB,EADsC;;AAItC,MAAA,IAAA,CAAKsB,cAAL,EAAA,CAAA;MAEA,IAAK9D,CAAAA,KAAL,CAAW+D,MAAX,CAAkB;AAAE1C,QAAAA,IAAI,EAAE,eAAR;AAAyB2C,QAAAA,KAAK,EAAE,IAAhC;AAAsCxB,QAAAA,QAAAA;OAAxD,CAAA,CAAA;AACD,KAAA;AACF,GAAA;;EAEDyB,cAAc,CAACzB,QAAD,EAAyD;AACrE,IAAA,IAAI,KAAKzC,SAAL,CAAe6D,QAAf,CAAwBpB,QAAxB,CAAJ,EAAuC;AACrC,MAAA,IAAA,CAAKzC,SAAL,GAAiB,IAAKA,CAAAA,SAAL,CAAemE,MAAf,CAAuBd,CAAD,IAAOA,CAAC,KAAKZ,QAAnC,CAAjB,CAAA;;AAEA,MAAA,IAAI,CAAC,IAAA,CAAKzC,SAAL,CAAec,MAApB,EAA4B;AAC1B;AACA;QACA,IAAI,IAAA,CAAKgB,OAAT,EAAkB;UAChB,IAAI,IAAA,CAAKlC,mBAAT,EAA8B;YAC5B,IAAKkC,CAAAA,OAAL,CAAaF,MAAb,CAAoB;AAAEwC,cAAAA,MAAM,EAAE,IAAA;aAA9B,CAAA,CAAA;AACD,WAFD,MAEO;YACL,IAAKtC,CAAAA,OAAL,CAAauC,WAAb,EAAA,CAAA;AACD,WAAA;AACF,SAAA;;AAED,QAAA,IAAA,CAAK5D,UAAL,EAAA,CAAA;AACD,OAAA;;MAED,IAAKR,CAAAA,KAAL,CAAW+D,MAAX,CAAkB;AAAE1C,QAAAA,IAAI,EAAE,iBAAR;AAA2B2C,QAAAA,KAAK,EAAE,IAAlC;AAAwCxB,QAAAA,QAAAA;OAA1D,CAAA,CAAA;AACD,KAAA;AACF,GAAA;;AAEDG,EAAAA,iBAAiB,GAAW;IAC1B,OAAO,IAAA,CAAK5C,SAAL,CAAec,MAAtB,CAAA;AACD,GAAA;;AAEDwD,EAAAA,UAAU,GAAS;AACjB,IAAA,IAAI,CAAC,IAAA,CAAK/D,KAAL,CAAWuC,aAAhB,EAA+B;AAC7B,MAAA,IAAA,CAAKzB,QAAL,CAAc;AAAEC,QAAAA,IAAI,EAAE,YAAA;OAAtB,CAAA,CAAA;AACD,KAAA;AACF,GAAA;;AAEDiD,EAAAA,KAAK,CACHxE,OADG,EAEHyE,YAFG,EAGa;AAAA,IAAA,IAAA,qBAAA,EAAA,qBAAA,CAAA;;AAChB,IAAA,IAAI,KAAKjE,KAAL,CAAWQ,WAAX,KAA2B,MAA/B,EAAuC;MACrC,IAAI,IAAA,CAAKR,KAAL,CAAWgB,aAAX,IAA4BiD,YAA5B,IAA4BA,IAAAA,IAAAA,YAAY,CAAEhB,aAA9C,EAA6D;AAC3D;AACA,QAAA,IAAA,CAAK5B,MAAL,CAAY;AAAES,UAAAA,MAAM,EAAE,IAAA;SAAtB,CAAA,CAAA;AACD,OAHD,MAGO,IAAI,IAAKR,CAAAA,OAAT,EAAkB;AAAA,QAAA,IAAA,cAAA,CAAA;;AACvB;AACA,QAAA,CAAA,cAAA,GAAA,IAAA,CAAKC,OAAL,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,cAAA,CAAc2C,aAAd,EAAA,CAFuB;;AAIvB,QAAA,OAAO,KAAK5C,OAAZ,CAAA;AACD,OAAA;AACF,KAXe;;;AAchB,IAAA,IAAI9B,OAAJ,EAAa;MACX,IAAKD,CAAAA,UAAL,CAAgBC,OAAhB,CAAA,CAAA;AACD,KAhBe;AAmBhB;;;AACA,IAAA,IAAI,CAAC,IAAA,CAAKA,OAAL,CAAa2E,OAAlB,EAA2B;AACzB,MAAA,MAAMjC,QAAQ,GAAG,IAAKzC,CAAAA,SAAL,CAAeoD,IAAf,CAAqBC,CAAD,IAAOA,CAAC,CAACtD,OAAF,CAAU2E,OAArC,CAAjB,CAAA;;AACA,MAAA,IAAIjC,QAAJ,EAAc;AACZ,QAAA,IAAA,CAAK3C,UAAL,CAAgB2C,QAAQ,CAAC1C,OAAzB,CAAA,CAAA;AACD,OAAA;AACF,KAAA;;AAED,IAAA,IAAI4E,OAAO,CAACC,GAAR,CAAYC,QAAZ,KAAyB,YAA7B,EAA2C;MACzC,IAAI,CAACC,KAAK,CAACC,OAAN,CAAc,KAAKhF,OAAL,CAAaK,QAA3B,CAAL,EAA2C;QACzC,IAAKF,CAAAA,MAAL,CAAY8E,KAAZ,CAAA,qIAAA,CAAA,CAAA;AAGD,OAAA;AACF,KAAA;;AAED,IAAA,MAAMC,eAAe,GAAGC,kBAAkB,EAA1C,CAnCgB;;AAsChB,IAAA,MAAMC,cAA+C,GAAG;MACtD/E,QAAQ,EAAE,KAAKA,QADuC;AAEtDgF,MAAAA,SAAS,EAAEC,SAF2C;AAGtD3E,MAAAA,IAAI,EAAE,IAAKA,CAAAA,IAAAA;AAH2C,KAAxD,CAtCgB;AA6ChB;AACA;;IACA,MAAM4E,iBAAiB,GAAIC,MAAD,IAAqB;AAC7CC,MAAAA,MAAM,CAACC,cAAP,CAAsBF,MAAtB,EAA8B,QAA9B,EAAwC;AACtCG,QAAAA,UAAU,EAAE,IAD0B;AAEtCC,QAAAA,GAAG,EAAE,MAAM;AACT,UAAA,IAAIV,eAAJ,EAAqB;YACnB,IAAKrF,CAAAA,mBAAL,GAA2B,IAA3B,CAAA;YACA,OAAOqF,eAAe,CAACW,MAAvB,CAAA;AACD,WAAA;;AACD,UAAA,OAAOP,SAAP,CAAA;AACD,SAAA;OARH,CAAA,CAAA;KADF,CAAA;;AAaAC,IAAAA,iBAAiB,CAACH,cAAD,CAAjB,CA5DgB;;IA+DhB,MAAMU,OAAO,GAAG,MAAM;AACpB,MAAA,IAAI,CAAC,IAAA,CAAK9F,OAAL,CAAa2E,OAAlB,EAA2B;QACzB,OAAOxC,OAAO,CAAC4D,MAAR,CAAA,gCAAA,GAC4B,KAAK/F,OAAL,CAAaM,SADzC,GAAP,GAAA,CAAA,CAAA;AAGD,OAAA;;MACD,IAAKT,CAAAA,mBAAL,GAA2B,KAA3B,CAAA;AACA,MAAA,OAAO,KAAKG,OAAL,CAAa2E,OAAb,CAAqBS,cAArB,CAAP,CAAA;AACD,KARD,CA/DgB;;;AA0EhB,IAAA,MAAMY,OAA6D,GAAG;MACpEvB,YADoE;MAEpEzE,OAAO,EAAE,KAAKA,OAFsD;MAGpEK,QAAQ,EAAE,KAAKA,QAHqD;MAIpEG,KAAK,EAAE,KAAKA,KAJwD;AAKpEsF,MAAAA,OAAAA;KALF,CAAA;IAQAP,iBAAiB,CAACS,OAAD,CAAjB,CAAA;IAEA,CAAKhG,qBAAAA,GAAAA,IAAAA,CAAAA,OAAL,CAAaiG,QAAb,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,qBAAA,CAAuBC,OAAvB,CAA+BF,OAA/B,EApFgB;;AAuFhB,IAAA,IAAA,CAAKG,WAAL,GAAmB,IAAK3F,CAAAA,KAAxB,CAvFgB;;AA0FhB,IAAA,IACE,KAAKA,KAAL,CAAWQ,WAAX,KAA2B,MAA3B,IACA,IAAKR,CAAAA,KAAL,CAAW4F,SAAX,MAAA,CAAA,qBAAA,GAAyBJ,OAAO,CAACvB,YAAjC,qBAAyB,qBAAsB9D,CAAAA,IAA/C,CAFF,EAGE;AAAA,MAAA,IAAA,sBAAA,CAAA;;AACA,MAAA,IAAA,CAAKW,QAAL,CAAc;AAAEC,QAAAA,IAAI,EAAE,OAAR;AAAiBZ,QAAAA,IAAI,EAAEqF,CAAAA,sBAAAA,GAAAA,OAAO,CAACvB,YAAV,qBAAE,sBAAsB9D,CAAAA,IAAAA;OAA3D,CAAA,CAAA;AACD,KAAA;;IAED,MAAM0F,OAAO,GAAIpB,KAAD,IAA0C;AACxD;MACA,IAAI,EAAEqB,gBAAgB,CAACrB,KAAD,CAAhB,IAA2BA,KAAK,CAAC3C,MAAnC,CAAJ,EAAgD;AAC9C,QAAA,IAAA,CAAKhB,QAAL,CAAc;AACZC,UAAAA,IAAI,EAAE,OADM;AAEZ0D,UAAAA,KAAK,EAAEA,KAAAA;SAFT,CAAA,CAAA;AAID,OAAA;;AAED,MAAA,IAAI,CAACqB,gBAAgB,CAACrB,KAAD,CAArB,EAA8B;AAAA,QAAA,IAAA,qBAAA,EAAA,kBAAA,EAAA,sBAAA,EAAA,mBAAA,CAAA;;AAC5B;QACA,CAAK/E,qBAAAA,GAAAA,CAAAA,kBAAAA,GAAAA,IAAAA,CAAAA,KAAL,CAAWN,MAAX,EAAkByG,OAAlB,KAA4BpB,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,qBAAAA,CAAAA,IAAAA,CAAAA,kBAAAA,EAAAA,KAA5B,EAAmC,IAAnC,CAAA,CAAA;AACA,QAAA,CAAA,sBAAA,GAAA,CAAA,mBAAA,GAAA,IAAA,CAAK/E,KAAL,CAAWN,MAAX,EAAkB2G,SAAlB,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,sBAAA,CAAA,IAAA,CAAA,mBAAA,EACE,IAAK/F,CAAAA,KAAL,CAAWY,IADb,EAEE6D,KAFF,EAGE,IAHF,CAAA,CAAA;;AAMA,QAAA,IAAIL,OAAO,CAACC,GAAR,CAAYC,QAAZ,KAAyB,YAA7B,EAA2C;AACzC,UAAA,IAAA,CAAK3E,MAAL,CAAY8E,KAAZ,CAAkBA,KAAlB,CAAA,CAAA;AACD,SAAA;AACF,OAAA;;MAED,IAAI,CAAC,IAAKuB,CAAAA,oBAAV,EAAgC;AAC9B;AACA,QAAA,IAAA,CAAK9F,UAAL,EAAA,CAAA;AACD,OAAA;;MACD,IAAK8F,CAAAA,oBAAL,GAA4B,KAA5B,CAAA;AACD,KA5BD,CAjGgB;;;IAgIhB,IAAKzE,CAAAA,OAAL,GAAe0E,aAAa,CAAC;MAC3BC,EAAE,EAAEV,OAAO,CAACF,OADe;MAE3Ba,KAAK,EAAEzB,eAAF,IAAA,IAAA,GAAA,KAAA,CAAA,GAAEA,eAAe,CAAEyB,KAAjB,CAAuBC,IAAvB,CAA4B1B,eAA5B,CAFoB;MAG3B2B,SAAS,EAAGzF,IAAD,IAAU;AAAA,QAAA,IAAA,sBAAA,EAAA,mBAAA,EAAA,sBAAA,EAAA,mBAAA,CAAA;;AACnB,QAAA,IAAI,OAAOA,IAAP,KAAgB,WAApB,EAAiC;AAC/B,UAAA,IAAIwD,OAAO,CAACC,GAAR,CAAYC,QAAZ,KAAyB,YAA7B,EAA2C;AACzC,YAAA,IAAA,CAAK3E,MAAL,CAAY8E,KAAZ,CAAA,wIAAA,GAC2I,KAAK3E,SADhJ,CAAA,CAAA;AAGD,WAAA;;AACD+F,UAAAA,OAAO,CAAC,IAAIS,KAAJ,CAAa,IAAKxG,CAAAA,SAAlB,wBAAD,CAAP,CAAA;AACA,UAAA,OAAA;AACD,SAAA;;AAED,QAAA,IAAA,CAAKY,OAAL,CAAaE,IAAb,CAAA,CAXmB;;QAcnB,CAAKlB,sBAAAA,GAAAA,CAAAA,mBAAAA,GAAAA,IAAAA,CAAAA,KAAL,CAAWN,MAAX,EAAkBiH,SAAlB,KAA8BzF,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,sBAAAA,CAAAA,IAAAA,CAAAA,mBAAAA,EAAAA,IAA9B,EAAoC,IAApC,CAAA,CAAA;AACA,QAAA,CAAA,sBAAA,GAAA,CAAA,mBAAA,GAAA,IAAA,CAAKlB,KAAL,CAAWN,MAAX,EAAkB2G,SAAlB,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,sBAAA,CAAA,IAAA,CAAA,mBAAA,EACEnF,IADF,EAEE,IAAKZ,CAAAA,KAAL,CAAWyE,KAFb,EAGE,IAHF,CAAA,CAAA;;QAMA,IAAI,CAAC,IAAKuB,CAAAA,oBAAV,EAAgC;AAC9B;AACA,UAAA,IAAA,CAAK9F,UAAL,EAAA,CAAA;AACD,SAAA;;QACD,IAAK8F,CAAAA,oBAAL,GAA4B,KAA5B,CAAA;OA5ByB;MA8B3BH,OA9B2B;AA+B3BU,MAAAA,MAAM,EAAE,CAACC,YAAD,EAAe/B,KAAf,KAAyB;AAC/B,QAAA,IAAA,CAAK3D,QAAL,CAAc;AAAEC,UAAAA,IAAI,EAAE,QAAR;UAAkByF,YAAlB;AAAgC/B,UAAAA,KAAAA;SAA9C,CAAA,CAAA;OAhCyB;AAkC3BgC,MAAAA,OAAO,EAAE,MAAM;AACb,QAAA,IAAA,CAAK3F,QAAL,CAAc;AAAEC,UAAAA,IAAI,EAAE,OAAA;SAAtB,CAAA,CAAA;OAnCyB;AAqC3B2F,MAAAA,UAAU,EAAE,MAAM;AAChB,QAAA,IAAA,CAAK5F,QAAL,CAAc;AAAEC,UAAAA,IAAI,EAAE,UAAA;SAAtB,CAAA,CAAA;OAtCyB;AAwC3B4F,MAAAA,KAAK,EAAEnB,OAAO,CAAChG,OAAR,CAAgBmH,KAxCI;AAyC3BC,MAAAA,UAAU,EAAEpB,OAAO,CAAChG,OAAR,CAAgBoH,UAzCD;AA0C3BC,MAAAA,WAAW,EAAErB,OAAO,CAAChG,OAAR,CAAgBqH,WAAAA;AA1CF,KAAD,CAA5B,CAAA;AA6CA,IAAA,IAAA,CAAKvF,OAAL,GAAe,IAAKC,CAAAA,OAAL,CAAaD,OAA5B,CAAA;AAEA,IAAA,OAAO,KAAKA,OAAZ,CAAA;AACD,GAAA;;EAEOR,QAAQ,CAACgG,MAAD,EAAsC;IACpD,MAAMC,OAAO,GACX/G,KADc,IAEgB;AAAA,MAAA,IAAA,YAAA,EAAA,qBAAA,CAAA;;MAC9B,QAAQ8G,MAAM,CAAC/F,IAAf;AACE,QAAA,KAAK,QAAL;UACE,OAAO,EACL,GAAGf,KADE;YAELgH,iBAAiB,EAAEF,MAAM,CAACN,YAFrB;YAGLS,kBAAkB,EAAEH,MAAM,CAACrC,KAAAA;WAH7B,CAAA;;AAKF,QAAA,KAAK,OAAL;UACE,OAAO,EACL,GAAGzE,KADE;AAELQ,YAAAA,WAAW,EAAE,QAAA;WAFf,CAAA;;AAIF,QAAA,KAAK,UAAL;UACE,OAAO,EACL,GAAGR,KADE;AAELQ,YAAAA,WAAW,EAAE,UAAA;WAFf,CAAA;;AAIF,QAAA,KAAK,OAAL;UACE,OAAO,EACL,GAAGR,KADE;AAELgH,YAAAA,iBAAiB,EAAE,CAFd;AAGLC,YAAAA,kBAAkB,EAAE,IAHf;AAILrB,YAAAA,SAAS,EAAEkB,CAAAA,YAAAA,GAAAA,MAAM,CAAC3G,IAAT,2BAAiB,IAJrB;YAKLK,WAAW,EAAE0G,QAAQ,CAAC,IAAK1H,CAAAA,OAAL,CAAaqH,WAAd,CAAR,GACT,UADS,GAET,QAPC;AAQL,YAAA,IAAI,CAAC7G,KAAK,CAACgB,aAAP,IAAwB;AAC1ByD,cAAAA,KAAK,EAAE,IADmB;AAE1B0C,cAAAA,MAAM,EAAE,SAAA;aAFV,CAAA;WARF,CAAA;;AAaF,QAAA,KAAK,SAAL;UACE,OAAO,EACL,GAAGnH,KADE;YAELY,IAAI,EAAEkG,MAAM,CAAClG,IAFR;AAGLwG,YAAAA,eAAe,EAAEpH,KAAK,CAACoH,eAAN,GAAwB,CAHpC;YAILpG,aAAa,EAAA,CAAA,qBAAA,GAAE8F,MAAM,CAAC9F,aAAT,oCAA0BqG,IAAI,CAACC,GAAL,EAJlC;AAKL7C,YAAAA,KAAK,EAAE,IALF;AAMLlC,YAAAA,aAAa,EAAE,KANV;AAOL4E,YAAAA,MAAM,EAAE,SAPH;AAQL,YAAA,IAAI,CAACL,MAAM,CAAC5F,MAAR,IAAkB;AACpBV,cAAAA,WAAW,EAAE,MADO;AAEpBwG,cAAAA,iBAAiB,EAAE,CAFC;AAGpBC,cAAAA,kBAAkB,EAAE,IAAA;aAHtB,CAAA;WARF,CAAA;;AAcF,QAAA,KAAK,OAAL;AACE,UAAA,MAAMxC,KAAK,GAAGqC,MAAM,CAACrC,KAArB,CAAA;;UAEA,IAAIqB,gBAAgB,CAACrB,KAAD,CAAhB,IAA2BA,KAAK,CAACZ,MAAjC,IAA2C,IAAK8B,CAAAA,WAApD,EAAiE;YAC/D,OAAO,EAAE,GAAG,IAAA,CAAKA,WAAV;AAAuBnF,cAAAA,WAAW,EAAE,MAAA;aAA3C,CAAA;AACD,WAAA;;UAED,OAAO,EACL,GAAGR,KADE;AAELyE,YAAAA,KAAK,EAAEA,KAFF;AAGL8C,YAAAA,gBAAgB,EAAEvH,KAAK,CAACuH,gBAAN,GAAyB,CAHtC;AAILC,YAAAA,cAAc,EAAEH,IAAI,CAACC,GAAL,EAJX;AAKLN,YAAAA,iBAAiB,EAAEhH,KAAK,CAACgH,iBAAN,GAA0B,CALxC;AAMLC,YAAAA,kBAAkB,EAAExC,KANf;AAOLjE,YAAAA,WAAW,EAAE,MAPR;AAQL2G,YAAAA,MAAM,EAAE,OAAA;WARV,CAAA;;AAUF,QAAA,KAAK,YAAL;UACE,OAAO,EACL,GAAGnH,KADE;AAELuC,YAAAA,aAAa,EAAE,IAAA;WAFjB,CAAA;;AAIF,QAAA,KAAK,UAAL;UACE,OAAO,EACL,GAAGvC,KADE;AAEL,YAAA,GAAG8G,MAAM,CAAC9G,KAAAA;WAFZ,CAAA;AArEJ,OAAA;KAHF,CAAA;;AA+EA,IAAA,IAAA,CAAKA,KAAL,GAAa+G,OAAO,CAAC,IAAA,CAAK/G,KAAN,CAApB,CAAA;IAEAyH,aAAa,CAACC,KAAd,CAAoB,MAAM;AACxB,MAAA,IAAA,CAAKjI,SAAL,CAAekI,OAAf,CAAwBzF,QAAD,IAAc;QACnCA,QAAQ,CAAC0F,aAAT,CAAuBd,MAAvB,CAAA,CAAA;OADF,CAAA,CAAA;MAIA,IAAKpH,CAAAA,KAAL,CAAW+D,MAAX,CAAkB;AAAEC,QAAAA,KAAK,EAAE,IAAT;AAAe3C,QAAAA,IAAI,EAAE,SAArB;AAAgC+F,QAAAA,MAAAA;OAAlD,CAAA,CAAA;KALF,CAAA,CAAA;AAOD,GAAA;;AAnciB,CAAA;;AAscpB,SAAS7G,eAAT,CAMET,OANF,EAO6B;AAC3B,EAAA,MAAMoB,IAAI,GACR,OAAOpB,OAAO,CAACqI,WAAf,KAA+B,UAA/B,GACKrI,OAAO,CAACqI,WAAT,EADJ,GAEIrI,OAAO,CAACqI,WAHd,CAAA;AAKA,EAAA,MAAMC,OAAO,GAAG,OAAOlH,IAAP,KAAgB,WAAhC,CAAA;EAEA,MAAMmH,oBAAoB,GAAGD,OAAO,GAChC,OAAOtI,OAAO,CAACuI,oBAAf,KAAwC,UAAxC,GACGvI,OAAO,CAACuI,oBAAT,EADF,GAEEvI,OAAO,CAACuI,oBAHsB,GAIhC,CAJJ,CAAA;EAMA,OAAO;IACLnH,IADK;AAELwG,IAAAA,eAAe,EAAE,CAFZ;AAGLpG,IAAAA,aAAa,EAAE8G,OAAO,GAAGC,oBAAH,IAAGA,IAAAA,GAAAA,oBAAH,GAA2BV,IAAI,CAACC,GAAL,EAA3B,GAAwC,CAHzD;AAIL7C,IAAAA,KAAK,EAAE,IAJF;AAKL8C,IAAAA,gBAAgB,EAAE,CALb;AAMLC,IAAAA,cAAc,EAAE,CANX;AAOLR,IAAAA,iBAAiB,EAAE,CAPd;AAQLC,IAAAA,kBAAkB,EAAE,IARf;AASLrB,IAAAA,SAAS,EAAE,IATN;AAULrD,IAAAA,aAAa,EAAE,KAVV;AAWL4E,IAAAA,MAAM,EAAEW,OAAO,GAAG,SAAH,GAAe,SAXzB;AAYLtH,IAAAA,WAAW,EAAE,MAAA;GAZf,CAAA;AAcD;;;;"}