{"version": 3, "file": "mutationObserver.esm.js", "sources": ["../../src/mutationObserver.ts"], "sourcesContent": ["import { getDefaultState } from './mutation'\nimport { notifyManager } from './notifyManager'\nimport { Subscribable } from './subscribable'\nimport { shallowEqualObjects } from './utils'\nimport type { QueryClient } from './queryClient'\nimport type {\n  MutateOptions,\n  MutationObserverBaseResult,\n  MutationObserverOptions,\n  MutationObserverResult,\n} from './types'\nimport type { Action, Mutation } from './mutation'\n\n// TYPES\n\ntype MutationObserverListener<TData, TError, TVariables, TContext> = (\n  result: MutationObserverResult<TData, TError, TVariables, TContext>,\n) => void\n\ninterface NotifyOptions {\n  listeners?: boolean\n  onError?: boolean\n  onSuccess?: boolean\n}\n\n// CLASS\n\nexport class MutationObserver<\n  TData = unknown,\n  TError = unknown,\n  TVariables = void,\n  TContext = unknown,\n> extends Subscribable<\n  MutationObserverListener<TData, TError, TVariables, TContext>\n> {\n  options!: MutationObserverOptions<TData, TError, TVariables, TContext>\n\n  private client: QueryClient\n  private currentResult!: MutationObserverResult<\n    TData,\n    TError,\n    TVariables,\n    TContext\n  >\n  private currentMutation?: Mutation<TData, TError, TVariables, TContext>\n  private mutateOptions?: MutateOptions<TData, TError, TVariables, TContext>\n\n  constructor(\n    client: QueryClient,\n    options: MutationObserverOptions<TData, TError, TVariables, TContext>,\n  ) {\n    super()\n\n    this.client = client\n    this.setOptions(options)\n    this.bindMethods()\n    this.updateResult()\n  }\n\n  protected bindMethods(): void {\n    this.mutate = this.mutate.bind(this)\n    this.reset = this.reset.bind(this)\n  }\n\n  setOptions(\n    options?: MutationObserverOptions<TData, TError, TVariables, TContext>,\n  ) {\n    const prevOptions = this.options\n    this.options = this.client.defaultMutationOptions(options)\n    if (!shallowEqualObjects(prevOptions, this.options)) {\n      this.client.getMutationCache().notify({\n        type: 'observerOptionsUpdated',\n        mutation: this.currentMutation,\n        observer: this,\n      })\n    }\n    this.currentMutation?.setOptions(this.options)\n  }\n\n  protected onUnsubscribe(): void {\n    if (!this.hasListeners()) {\n      this.currentMutation?.removeObserver(this)\n    }\n  }\n\n  onMutationUpdate(action: Action<TData, TError, TVariables, TContext>): void {\n    this.updateResult()\n\n    // Determine which callbacks to trigger\n    const notifyOptions: NotifyOptions = {\n      listeners: true,\n    }\n\n    if (action.type === 'success') {\n      notifyOptions.onSuccess = true\n    } else if (action.type === 'error') {\n      notifyOptions.onError = true\n    }\n\n    this.notify(notifyOptions)\n  }\n\n  getCurrentResult(): MutationObserverResult<\n    TData,\n    TError,\n    TVariables,\n    TContext\n  > {\n    return this.currentResult\n  }\n\n  reset(): void {\n    this.currentMutation = undefined\n    this.updateResult()\n    this.notify({ listeners: true })\n  }\n\n  mutate(\n    variables?: TVariables,\n    options?: MutateOptions<TData, TError, TVariables, TContext>,\n  ): Promise<TData> {\n    this.mutateOptions = options\n\n    if (this.currentMutation) {\n      this.currentMutation.removeObserver(this)\n    }\n\n    this.currentMutation = this.client.getMutationCache().build(this.client, {\n      ...this.options,\n      variables:\n        typeof variables !== 'undefined' ? variables : this.options.variables,\n    })\n\n    this.currentMutation.addObserver(this)\n\n    return this.currentMutation.execute()\n  }\n\n  private updateResult(): void {\n    const state = this.currentMutation\n      ? this.currentMutation.state\n      : getDefaultState<TData, TError, TVariables, TContext>()\n\n    const isLoading = state.status === 'loading'\n    const result: MutationObserverBaseResult<\n      TData,\n      TError,\n      TVariables,\n      TContext\n    > = {\n      ...state,\n      isLoading,\n      isPending: isLoading,\n      isSuccess: state.status === 'success',\n      isError: state.status === 'error',\n      isIdle: state.status === 'idle',\n      mutate: this.mutate,\n      reset: this.reset,\n    }\n\n    this.currentResult = result as MutationObserverResult<\n      TData,\n      TError,\n      TVariables,\n      TContext\n    >\n  }\n\n  private notify(options: NotifyOptions) {\n    notifyManager.batch(() => {\n      // First trigger the mutate callbacks\n      if (this.mutateOptions && this.hasListeners()) {\n        if (options.onSuccess) {\n          this.mutateOptions.onSuccess?.(\n            this.currentResult.data!,\n            this.currentResult.variables!,\n            this.currentResult.context!,\n          )\n          this.mutateOptions.onSettled?.(\n            this.currentResult.data!,\n            null,\n            this.currentResult.variables!,\n            this.currentResult.context,\n          )\n        } else if (options.onError) {\n          this.mutateOptions.onError?.(\n            this.currentResult.error!,\n            this.currentResult.variables!,\n            this.currentResult.context,\n          )\n          this.mutateOptions.onSettled?.(\n            undefined,\n            this.currentResult.error,\n            this.currentResult.variables!,\n            this.currentResult.context,\n          )\n        }\n      }\n\n      // Then trigger the listeners\n      if (options.listeners) {\n        this.listeners.forEach(({ listener }) => {\n          listener(this.currentResult)\n        })\n      }\n    })\n  }\n}\n"], "names": ["MutationObserver", "Subscribable", "constructor", "client", "options", "setOptions", "bindMethods", "updateResult", "mutate", "bind", "reset", "prevOptions", "defaultMutationOptions", "shallowEqualObjects", "getMutationCache", "notify", "type", "mutation", "currentMutation", "observer", "onUnsubscribe", "hasListeners", "removeObserver", "onMutationUpdate", "action", "notifyOptions", "listeners", "onSuccess", "onError", "getCurrentResult", "currentResult", "undefined", "variables", "mutateOptions", "build", "addObserver", "execute", "state", "getDefaultState", "isLoading", "status", "result", "isPending", "isSuccess", "isError", "isIdle", "notify<PERSON><PERSON>ger", "batch", "data", "context", "onSettled", "error", "for<PERSON>ach", "listener"], "mappings": ";;;;;AAyBA;AAEO,MAAMA,gBAAN,SAKGC,YALH,CAOL;AAaAC,EAAAA,WAAW,CACTC,MADS,EAETC,OAFS,EAGT;AACA,IAAA,KAAA,EAAA,CAAA;IAEA,IAAKD,CAAAA,MAAL,GAAcA,MAAd,CAAA;IACA,IAAKE,CAAAA,UAAL,CAAgBD,OAAhB,CAAA,CAAA;AACA,IAAA,IAAA,CAAKE,WAAL,EAAA,CAAA;AACA,IAAA,IAAA,CAAKC,YAAL,EAAA,CAAA;AACD,GAAA;;AAESD,EAAAA,WAAW,GAAS;IAC5B,IAAKE,CAAAA,MAAL,GAAc,IAAKA,CAAAA,MAAL,CAAYC,IAAZ,CAAiB,IAAjB,CAAd,CAAA;IACA,IAAKC,CAAAA,KAAL,GAAa,IAAKA,CAAAA,KAAL,CAAWD,IAAX,CAAgB,IAAhB,CAAb,CAAA;AACD,GAAA;;EAEDJ,UAAU,CACRD,OADQ,EAER;AAAA,IAAA,IAAA,qBAAA,CAAA;;IACA,MAAMO,WAAW,GAAG,IAAA,CAAKP,OAAzB,CAAA;IACA,IAAKA,CAAAA,OAAL,GAAe,IAAKD,CAAAA,MAAL,CAAYS,sBAAZ,CAAmCR,OAAnC,CAAf,CAAA;;IACA,IAAI,CAACS,mBAAmB,CAACF,WAAD,EAAc,IAAKP,CAAAA,OAAnB,CAAxB,EAAqD;AACnD,MAAA,IAAA,CAAKD,MAAL,CAAYW,gBAAZ,EAAA,CAA+BC,MAA/B,CAAsC;AACpCC,QAAAA,IAAI,EAAE,wBAD8B;QAEpCC,QAAQ,EAAE,KAAKC,eAFqB;AAGpCC,QAAAA,QAAQ,EAAE,IAAA;OAHZ,CAAA,CAAA;AAKD,KAAA;;AACD,IAAA,CAAA,qBAAA,GAAA,IAAA,CAAKD,eAAL,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,qBAAA,CAAsBb,UAAtB,CAAiC,KAAKD,OAAtC,CAAA,CAAA;AACD,GAAA;;AAESgB,EAAAA,aAAa,GAAS;AAC9B,IAAA,IAAI,CAAC,IAAA,CAAKC,YAAL,EAAL,EAA0B;AAAA,MAAA,IAAA,sBAAA,CAAA;;AACxB,MAAA,CAAA,sBAAA,GAAA,IAAA,CAAKH,eAAL,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,sBAAA,CAAsBI,cAAtB,CAAqC,IAArC,CAAA,CAAA;AACD,KAAA;AACF,GAAA;;EAEDC,gBAAgB,CAACC,MAAD,EAA4D;IAC1E,IAAKjB,CAAAA,YAAL,GAD0E;;AAI1E,IAAA,MAAMkB,aAA4B,GAAG;AACnCC,MAAAA,SAAS,EAAE,IAAA;KADb,CAAA;;AAIA,IAAA,IAAIF,MAAM,CAACR,IAAP,KAAgB,SAApB,EAA+B;MAC7BS,aAAa,CAACE,SAAd,GAA0B,IAA1B,CAAA;AACD,KAFD,MAEO,IAAIH,MAAM,CAACR,IAAP,KAAgB,OAApB,EAA6B;MAClCS,aAAa,CAACG,OAAd,GAAwB,IAAxB,CAAA;AACD,KAAA;;IAED,IAAKb,CAAAA,MAAL,CAAYU,aAAZ,CAAA,CAAA;AACD,GAAA;;AAEDI,EAAAA,gBAAgB,GAKd;AACA,IAAA,OAAO,KAAKC,aAAZ,CAAA;AACD,GAAA;;AAEDpB,EAAAA,KAAK,GAAS;IACZ,IAAKQ,CAAAA,eAAL,GAAuBa,SAAvB,CAAA;AACA,IAAA,IAAA,CAAKxB,YAAL,EAAA,CAAA;AACA,IAAA,IAAA,CAAKQ,MAAL,CAAY;AAAEW,MAAAA,SAAS,EAAE,IAAA;KAAzB,CAAA,CAAA;AACD,GAAA;;AAEDlB,EAAAA,MAAM,CACJwB,SADI,EAEJ5B,OAFI,EAGY;IAChB,IAAK6B,CAAAA,aAAL,GAAqB7B,OAArB,CAAA;;IAEA,IAAI,IAAA,CAAKc,eAAT,EAA0B;AACxB,MAAA,IAAA,CAAKA,eAAL,CAAqBI,cAArB,CAAoC,IAApC,CAAA,CAAA;AACD,KAAA;;AAED,IAAA,IAAA,CAAKJ,eAAL,GAAuB,IAAKf,CAAAA,MAAL,CAAYW,gBAAZ,EAAA,CAA+BoB,KAA/B,CAAqC,KAAK/B,MAA1C,EAAkD,EACvE,GAAG,KAAKC,OAD+D;MAEvE4B,SAAS,EACP,OAAOA,SAAP,KAAqB,WAArB,GAAmCA,SAAnC,GAA+C,IAAK5B,CAAAA,OAAL,CAAa4B,SAAAA;AAHS,KAAlD,CAAvB,CAAA;AAMA,IAAA,IAAA,CAAKd,eAAL,CAAqBiB,WAArB,CAAiC,IAAjC,CAAA,CAAA;AAEA,IAAA,OAAO,IAAKjB,CAAAA,eAAL,CAAqBkB,OAArB,EAAP,CAAA;AACD,GAAA;;AAEO7B,EAAAA,YAAY,GAAS;IAC3B,MAAM8B,KAAK,GAAG,IAAA,CAAKnB,eAAL,GACV,IAAKA,CAAAA,eAAL,CAAqBmB,KADX,GAEVC,eAAe,EAFnB,CAAA;AAIA,IAAA,MAAMC,SAAS,GAAGF,KAAK,CAACG,MAAN,KAAiB,SAAnC,CAAA;AACA,IAAA,MAAMC,MAKL,GAAG,EACF,GAAGJ,KADD;MAEFE,SAFE;AAGFG,MAAAA,SAAS,EAAEH,SAHT;AAIFI,MAAAA,SAAS,EAAEN,KAAK,CAACG,MAAN,KAAiB,SAJ1B;AAKFI,MAAAA,OAAO,EAAEP,KAAK,CAACG,MAAN,KAAiB,OALxB;AAMFK,MAAAA,MAAM,EAAER,KAAK,CAACG,MAAN,KAAiB,MANvB;MAOFhC,MAAM,EAAE,KAAKA,MAPX;AAQFE,MAAAA,KAAK,EAAE,IAAKA,CAAAA,KAAAA;KAbd,CAAA;IAgBA,IAAKoB,CAAAA,aAAL,GAAqBW,MAArB,CAAA;AAMD,GAAA;;EAEO1B,MAAM,CAACX,OAAD,EAAyB;IACrC0C,aAAa,CAACC,KAAd,CAAoB,MAAM;AACxB;AACA,MAAA,IAAI,KAAKd,aAAL,IAAsB,IAAKZ,CAAAA,YAAL,EAA1B,EAA+C;QAC7C,IAAIjB,OAAO,CAACuB,SAAZ,EAAuB;AAAA,UAAA,IAAA,qBAAA,EAAA,mBAAA,EAAA,sBAAA,EAAA,oBAAA,CAAA;;AACrB,UAAA,CAAA,qBAAA,GAAA,CAAA,mBAAA,GAAA,IAAA,CAAKM,aAAL,EAAmBN,SAAnB,KACE,IAAA,GAAA,KAAA,CAAA,GAAA,qBAAA,CAAA,IAAA,CAAA,mBAAA,EAAA,IAAA,CAAKG,aAAL,CAAmBkB,IADrB,EAEE,IAAA,CAAKlB,aAAL,CAAmBE,SAFrB,EAGE,IAAKF,CAAAA,aAAL,CAAmBmB,OAHrB,CAAA,CAAA;AAKA,UAAA,CAAA,sBAAA,GAAA,CAAA,oBAAA,GAAA,IAAA,CAAKhB,aAAL,EAAmBiB,SAAnB,uEACE,IAAKpB,CAAAA,aAAL,CAAmBkB,IADrB,EAEE,IAFF,EAGE,IAAA,CAAKlB,aAAL,CAAmBE,SAHrB,EAIE,IAAKF,CAAAA,aAAL,CAAmBmB,OAJrB,CAAA,CAAA;AAMD,SAZD,MAYO,IAAI7C,OAAO,CAACwB,OAAZ,EAAqB;AAAA,UAAA,IAAA,sBAAA,EAAA,oBAAA,EAAA,sBAAA,EAAA,oBAAA,CAAA;;AAC1B,UAAA,CAAA,sBAAA,GAAA,CAAA,oBAAA,GAAA,IAAA,CAAKK,aAAL,EAAmBL,OAAnB,KACE,IAAA,GAAA,KAAA,CAAA,GAAA,sBAAA,CAAA,IAAA,CAAA,oBAAA,EAAA,IAAA,CAAKE,aAAL,CAAmBqB,KADrB,EAEE,IAAA,CAAKrB,aAAL,CAAmBE,SAFrB,EAGE,IAAKF,CAAAA,aAAL,CAAmBmB,OAHrB,CAAA,CAAA;AAKA,UAAA,CAAA,sBAAA,GAAA,CAAA,oBAAA,GAAA,IAAA,CAAKhB,aAAL,EAAmBiB,SAAnB,uEACEnB,SADF,EAEE,KAAKD,aAAL,CAAmBqB,KAFrB,EAGE,IAAA,CAAKrB,aAAL,CAAmBE,SAHrB,EAIE,IAAKF,CAAAA,aAAL,CAAmBmB,OAJrB,CAAA,CAAA;AAMD,SAAA;AACF,OA5BuB;;;MA+BxB,IAAI7C,OAAO,CAACsB,SAAZ,EAAuB;AACrB,QAAA,IAAA,CAAKA,SAAL,CAAe0B,OAAf,CAAuB,CAAC;AAAEC,UAAAA,QAAAA;AAAF,SAAD,KAAkB;UACvCA,QAAQ,CAAC,IAAKvB,CAAAA,aAAN,CAAR,CAAA;SADF,CAAA,CAAA;AAGD,OAAA;KAnCH,CAAA,CAAA;AAqCD,GAAA;;AA5KD;;;;"}