from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
import logging
import httpx
import time
from typing import List, Optional
from pathlib import Path

from app.api.deps import get_db, get_current_active_user
from app.models.user import User
from app.models.audio_recording import AudioRecording
from app.models.transcription import Transcription
from app.core.config import settings

logger = logging.getLogger(__name__)

router = APIRouter()

@router.post("/transcribe/{audio_id}")
async def transcribe_audio(
    audio_id: str,
    language: Optional[str] = Query(None, description="Language code (e.g., 'nl', 'en') or 'auto' for auto-detection"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Transcribe audio to text using Whisper"""
    try:
        # Get audio recording
        audio = db.query(AudioRecording).filter(
            AudioRecording.id == audio_id,
            AudioRecording.user_id == current_user.id
        ).first()

        if not audio:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Audio recording not found"
            )

        # Check if already transcribed
        existing_transcription = db.query(Transcription).filter(
            Transcription.audio_recording_id == audio.id
        ).first()

        if existing_transcription:
            return {
                "id": str(existing_transcription.id),
                "text": existing_transcription.raw_text,
                "language": existing_transcription.language,
                "confidence_score": existing_transcription.confidence_score,
                "status": "already_transcribed"
            }

        # Check if file exists
        file_path = Path(audio.file_path)
        if not file_path.exists():
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Audio file not found on disk"
            )

        # Update audio status
        audio.status = "transcribing"
        db.commit()

        try:
            # Call Whisper service
            async with httpx.AsyncClient(timeout=300.0) as client:
                with open(file_path, 'rb') as f:
                    files = {"file": (audio.filename, f, audio.mime_type)}
                    params = {}
                    if language and language != "auto":
                        params["language"] = language

                    headers = {}
                    if getattr(settings, "RUNPOD_API_KEY", None):
                        headers["Authorization"] = f"Bearer {settings.RUNPOD_API_KEY}"

                    response = await client.post(
                        f"{settings.WHISPER_SERVICE_URL}/transcribe",
                        files=files,
                        params=params,
                        headers=headers  # << toegevoegd
                    )


                    if response.status_code != 200:
                        raise HTTPException(
                            status_code=status.HTTP_502_BAD_GATEWAY,
                            detail=f"Whisper service error: {response.text}"
                        )

                    whisper_result = response.json()

            # Create transcription record
            transcription = Transcription(
                audio_recording_id=audio.id,
                raw_text=whisper_result["text"],
                confidence_score=whisper_result.get("confidence_score"),
                language=whisper_result.get("language", "unknown"),
                processing_time_ms=whisper_result.get("processing_time_ms")
            )

            db.add(transcription)

            # Update audio status and duration if available
            audio.status = "transcribed"
            if "duration_seconds" in whisper_result:
                audio.duration_seconds = whisper_result["duration_seconds"]

            db.commit()
            db.refresh(transcription)

            logger.info(f"Audio {audio.filename} transcribed successfully for {current_user.email}")

            return {
                "id": str(transcription.id),
                "text": transcription.raw_text,
                "language": transcription.language,
                "confidence_score": transcription.confidence_score,
                "processing_time_ms": transcription.processing_time_ms,
                "status": "completed"
            }

        except httpx.TimeoutException:
            audio.status = "transcription_failed"
            db.commit()
            raise HTTPException(
                status_code=status.HTTP_504_GATEWAY_TIMEOUT,
                detail="Transcription service timeout"
            )
        except httpx.RequestError as e:
            audio.status = "transcription_failed"
            db.commit()
            raise HTTPException(
                status_code=status.HTTP_502_BAD_GATEWAY,
                detail=f"Transcription service unavailable: {str(e)}"
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Transcription error for audio {audio_id}: {e}")
        # Update audio status on error
        if 'audio' in locals():
            audio.status = "transcription_failed"
            db.commit()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Transcription failed"
        )

@router.get("/", response_model=List[dict])
async def get_user_transcriptions(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000)
):
    """Get user's transcriptions"""
    try:
        # Get transcriptions for user's audio recordings
        transcriptions = db.query(Transcription).join(AudioRecording).filter(
            AudioRecording.user_id == current_user.id
        ).order_by(Transcription.created_at.desc()).offset(skip).limit(limit).all()

        return [
            {
                "id": str(t.id),
                "audio_recording_id": str(t.audio_recording_id),
                "text": t.raw_text[:200] + "..." if len(t.raw_text) > 200 else t.raw_text,
                "language": t.language,
                "confidence_score": t.confidence_score,
                "created_at": t.created_at
            }
            for t in transcriptions
        ]

    except Exception as e:
        logger.error(f"Error fetching transcriptions: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error fetching transcriptions"
        )

@router.get("/{transcription_id}")
async def get_transcription(
    transcription_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Get transcription details"""
    try:
        transcription = db.query(Transcription).join(AudioRecording).filter(
            Transcription.id == transcription_id,
            AudioRecording.user_id == current_user.id
        ).first()

        if not transcription:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Transcription not found"
            )

        return {
            "id": str(transcription.id),
            "audio_recording_id": str(transcription.audio_recording_id),
            "raw_text": transcription.raw_text,
            "pseudonymized_text": transcription.pseudonymized_text,
            "language": transcription.language,
            "confidence_score": transcription.confidence_score,
            "processing_time_ms": transcription.processing_time_ms,
            "created_at": transcription.created_at
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error fetching transcription {transcription_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error fetching transcription"
        )

@router.post("/{transcription_id}/pseudonymize")
async def pseudonymize_transcription(
    transcription_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Pseudonymize transcription text"""
    try:
        from app.services.pseudonymizer import get_pseudonymizer

        # Get transcription
        transcription = db.query(Transcription).join(AudioRecording).filter(
            Transcription.id == transcription_id,
            AudioRecording.user_id == current_user.id
        ).first()

        if not transcription:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Transcription not found"
            )

        # Check if already pseudonymized
        if transcription.pseudonymized_text:
            return {
                "id": str(transcription.id),
                "pseudonymized_text": transcription.pseudonymized_text,
                "status": "already_pseudonymized"
            }

        # Pseudonymize the text
        pseudonymizer = get_pseudonymizer(db)
        pseudonymized_text, mappings = pseudonymizer.pseudonymize_text(
            transcription.raw_text,
            str(transcription.id)
        )

        # Update transcription with pseudonymized text
        transcription.pseudonymized_text = pseudonymized_text
        db.commit()
        db.refresh(transcription)

        # Get PII summary
        pii_summary = pseudonymizer.get_pii_summary(str(transcription.id))

        logger.info(f"Transcription {transcription_id} pseudonymized successfully")

        return {
            "id": str(transcription.id),
            "pseudonymized_text": pseudonymized_text,
            "pii_detected": len(mappings),
            "pii_summary": pii_summary,
            "status": "completed"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Pseudonymization error for transcription {transcription_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Pseudonymization failed"
        )

@router.post("/{transcription_id}/restore")
async def restore_transcription(
    transcription_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Restore original text from pseudonymized transcription"""
    try:
        from app.services.pseudonymizer import get_pseudonymizer

        # Get transcription
        transcription = db.query(Transcription).join(AudioRecording).filter(
            Transcription.id == transcription_id,
            AudioRecording.user_id == current_user.id
        ).first()

        if not transcription:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Transcription not found"
            )

        if not transcription.pseudonymized_text:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Transcription is not pseudonymized"
            )

        # Restore the text
        pseudonymizer = get_pseudonymizer(db)
        restored_text = pseudonymizer.restore_text(
            transcription.pseudonymized_text,
            str(transcription.id)
        )

        return {
            "id": str(transcription.id),
            "original_text": transcription.raw_text,
            "restored_text": restored_text,
            "status": "restored"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Restoration error for transcription {transcription_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Text restoration failed"
        )

@router.get("/{transcription_id}/pii-summary")
async def get_pii_summary(
    transcription_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Get PII summary for transcription"""
    try:
        from app.services.pseudonymizer import get_pseudonymizer

        # Verify transcription exists and belongs to user
        transcription = db.query(Transcription).join(AudioRecording).filter(
            Transcription.id == transcription_id,
            AudioRecording.user_id == current_user.id
        ).first()

        if not transcription:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Transcription not found"
            )

        # Get PII summary
        pseudonymizer = get_pseudonymizer(db)
        pii_summary = pseudonymizer.get_pii_summary(str(transcription.id))

        return {
            "transcription_id": str(transcription.id),
            "pii_summary": pii_summary,
            "total_pii_items": sum(pii_summary.values()),
            "is_pseudonymized": bool(transcription.pseudonymized_text)
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting PII summary for transcription {transcription_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error getting PII summary"
        )
